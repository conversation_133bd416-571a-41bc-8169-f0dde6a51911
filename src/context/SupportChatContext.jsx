import {
  createContext,
  useReducer,
  useEffect,
  useRef,
  useContext,
  useCallback,
  useMemo,
} from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { useOptimizedPolling } from "Context/OptimizedPollingContext";
import { EventListener } from "Utils/EventListener";

let sdk = new MkdSDK();

export const SupportChatContext = createContext();

const initialState = {
  rooms: [],
  unreadMessages: 0,
  isPolling: false,
  lastPollingTime: null,
  roomMessages: new Map(), // roomId -> messages array
  lastMessageIds: new Map(), // roomId -> Set of message IDs for deduplication
};

const reducer = (state, action) => {
  switch (action.type) {
    case "SET_ROOMS":
      return { ...state, rooms: action.payload };

    case "SET_UNREAD_MESSAGES":
      return { ...state, unreadMessages: action.payload };

    case "SET_POLLING_STATUS":
      return { ...state, isPolling: action.payload };

    case "SET_LAST_POLLING_TIME":
      return { ...state, lastPollingTime: action.payload };

    case "UPDATE_ROOM_UNREAD":
      return {
        ...state,
        rooms: state.rooms.map((room) =>
          room.id === action.payload.roomId
            ? { ...room, unread: action.payload.unreadCount }
            : room
        ),
        unreadMessages: state.rooms.reduce((total, room) => {
          if (room.id === action.payload.roomId) {
            return total - room.unread + action.payload.unreadCount;
          }
          return total + room.unread;
        }, 0),
      };

    case "SET_ROOM_MESSAGES":
      const newRoomMessages = new Map(state.roomMessages);
      newRoomMessages.set(action.payload.roomId, action.payload.messages);
      return {
        ...state,
        roomMessages: newRoomMessages,
      };

    case "ADD_ROOM_MESSAGES":
      const updatedRoomMessages = new Map(state.roomMessages);
      const existingMessages =
        updatedRoomMessages.get(action.payload.roomId) || [];
      const newMessages = action.payload.messages.filter(
        (newMsg) =>
          !existingMessages.some((existing) => existing.id === newMsg.id)
      );
      if (newMessages.length > 0) {
        updatedRoomMessages.set(action.payload.roomId, [
          ...existingMessages,
          ...newMessages,
        ]);
      }
      return {
        ...state,
        roomMessages: updatedRoomMessages,
      };

    case "UPDATE_MESSAGE_IDS":
      const newLastMessageIds = new Map(state.lastMessageIds);
      newLastMessageIds.set(
        action.payload.roomId,
        new Set(action.payload.messageIds)
      );
      return {
        ...state,
        lastMessageIds: newLastMessageIds,
      };

    default:
      return state;
  }
};

export const SupportChatProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const isVisibleRef = useRef(true);
  const previousUnreadCountRef = useRef(0);
  const { state: authState, dispatch: authDispatch } = useContext(AuthContext);
  const { subscribe, setPollingConfig } = useOptimizedPolling();

  // Enhanced EventListener for chat-specific pub/sub
  const chatEventListener = useRef(new EventListener({ name: "SupportChat" }));
  const roomSubscriptions = useRef(new Map()); // roomId -> unsubscribe function

  // Function to play notification sound
  const playNotificationSound = () => {
    try {
      // eslint-disable-next-line
      const audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.3
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.log("Could not play notification sound:", error);
    }
  };

  // Memoized function to calculate unread messages for a room
  const calculateUnreadCount = useCallback(
    (room, messages) => {
      if (!messages) return 0;

      const roomMessages = messages.filter((msg) => msg.room_id === room.id);

      return roomMessages.reduce((total, msg) => {
        try {
          const chatData = JSON.parse(msg.chat);
          // Only count messages as unread if they're from other users and have unread=1
          if (
            msg.unread === 1 &&
            chatData.user_id !== authState.user?.toString()
          ) {
            return total + 1;
          }
        } catch (parseError) {
          console.log("Error parsing chat data:", parseError);
        }
        return total;
      }, 0);
    },
    [authState.user]
  );

  // Enhanced function to subscribe to room messages
  const subscribeToRoomMessages = useCallback((roomId, callback) => {
    const unsubscribe = chatEventListener.current.subscribeToRoom(
      roomId,
      callback
    );
    roomSubscriptions.current.set(roomId, unsubscribe);
    return unsubscribe;
  }, []);

  // Enhanced function to publish room messages
  const publishRoomMessages = useCallback((roomId, messages, options = {}) => {
    chatEventListener.current.triggerRoomMessages(roomId, messages, options);
  }, []);

  // Function to get messages for a specific room
  const getRoomMessages = useCallback(
    (roomId) => {
      return state.roomMessages.get(roomId) || [];
    },
    [state.roomMessages]
  );

  // Function to get all rooms with unread counts
  async function getRooms(force = false) {
    try {
      // Add throttling to prevent excessive API calls
      const now = Date.now();
      const lastFetch = state.lastPollingTime
        ? new Date(state.lastPollingTime).getTime()
        : 0;
      const timeSinceLastFetch = now - lastFetch;

      // Don't fetch if less than 15 seconds have passed unless forced
      if (!force && timeSinceLastFetch < 15000) {
        console.log("⏳ Skipping getRooms - too soon since last fetch");
        return;
      }

      console.log("🔄 SupportChatContext getRooms executing", {
        force,
        authUser: authState.user,
      });
      dispatch({ type: "SET_POLLING_STATUS", payload: true });

      console.log("📡 SupportChatContext: Calling sdk.getMyRoom()");
      const response = await sdk.getMyRoom();
      console.log("Support chat rooms response:", response);
      console.log("Response structure:", {
        hasResponse: !!response,
        hasList: !!(response && response.list),
        listLength: response?.list?.length || 0,
        hasMessages: !!(response && response.messages),
        messagesLength: response?.messages?.length || 0,
      });

      if (response && response.list && response.list.length > 0) {
        // Process rooms and calculate unread counts
        const processedRooms = response.list.map((room) => {
          const unreadCount = calculateUnreadCount(room, response.messages);
          return {
            ...room,
            unread: unreadCount,
          };
        });

        // Calculate total unread messages
        const totalUnread = processedRooms.reduce(
          (total, room) => total + room.unread,
          0
        );

        // Check if there are new unread messages and play sound
        const previousCount = previousUnreadCountRef.current;
        if (totalUnread > previousCount && totalUnread > 0) {
          playNotificationSound();
        }

        dispatch({ type: "SET_ROOMS", payload: processedRooms });
        dispatch({ type: "SET_UNREAD_MESSAGES", payload: totalUnread });
        dispatch({ type: "SET_LAST_POLLING_TIME", payload: new Date() });

        previousUnreadCountRef.current = totalUnread;
      } else {
        dispatch({ type: "SET_ROOMS", payload: [] });
        dispatch({ type: "SET_UNREAD_MESSAGES", payload: 0 });
        previousUnreadCountRef.current = 0;
      }
    } catch (error) {
      console.error("Error fetching support chat rooms:", error);
      tokenExpireError(authDispatch, error.message);
    } finally {
      dispatch({ type: "SET_POLLING_STATUS", payload: false });
    }
  }

  // Function to mark messages as read for a specific room
  async function markMessagesAsRead(roomId) {
    try {
      // Get the room data to find unread messages
      const response = await sdk.getMyRoom();

      if (response && response.messages) {
        // Find unread messages for this room that are from other users
        const unreadMessages = response.messages
          .filter((msg) => {
            if (msg.room_id !== roomId || msg.unread !== 1) return false;

            try {
              const chatData = JSON.parse(msg.chat);
              // Only mark messages from other users as read
              return chatData.user_id !== authState.user?.toString();
            } catch (parseError) {
              console.log("Error parsing chat data:", parseError);
              return false;
            }
          })
          .map((msg) => msg.id);

        if (unreadMessages.length > 0) {
          // Set table to chat and update each message individually
          sdk.setTable("chat");
          await Promise.all(
            unreadMessages.map((id) =>
              sdk.callRestAPI({ id, unread: 0 }, "PUT")
            )
          );

          console.log(
            `Marked ${unreadMessages.length} messages as read for room ${roomId}`
          );
        }
      }

      // Update local state
      dispatch({
        type: "UPDATE_ROOM_UNREAD",
        payload: { roomId, unreadCount: 0 },
      });

      // Refresh rooms to get updated counts
      await getRooms();
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  }

  useEffect(() => {
    console.log("🔐 SupportChatContext useEffect triggered", {
      user: authState.user,
      isAuth: authState.isAuthenticated,
    });
    // Only set up polling and fetch data if user is authenticated
    if (!authState.user || !authState.isAuthenticated) {
      console.log("🚫 User not authenticated - skipping polling setup");
      return;
    }

    // Set up optimized polling configuration
    setPollingConfig(authState.user, authDispatch);

    // Initial fetch - debounced to prevent excessive calls
    const timeoutId = setTimeout(() => {
      getRooms();
    }, 500); // Increased delay to prevent conflicts

    // Handle visibility change with throttling
    const handleVisibilityChange = () => {
      isVisibleRef.current = document.visibilityState === "visible";
      if (isVisibleRef.current) {
        // Only fetch rooms when becoming visible, with throttling
        setTimeout(() => {
          getRooms(false); // Don't force, let throttling handle it
        }, 1000); // Wait 1 second after becoming visible
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Subscribe to optimized polling for rooms updates
    const unsubscribeRooms = subscribe("rooms", (roomsResponse) => {
      console.log("📡 Received rooms update from optimized polling");
      if (
        roomsResponse &&
        roomsResponse.list &&
        roomsResponse.list.length > 0
      ) {
        // Process rooms and calculate unread counts
        const processedRooms = roomsResponse.list.map((room) => {
          const unreadCount = calculateUnreadCount(
            room,
            roomsResponse.messages
          );
          return {
            ...room,
            unread: unreadCount,
          };
        });

        // Calculate total unread messages
        const totalUnread = processedRooms.reduce(
          (total, room) => total + room.unread,
          0
        );

        // Check if there are new unread messages and play sound
        const previousCount = previousUnreadCountRef.current;
        if (totalUnread > previousCount && totalUnread > 0) {
          playNotificationSound();
        }

        dispatch({ type: "SET_ROOMS", payload: processedRooms });
        dispatch({ type: "SET_UNREAD_MESSAGES", payload: totalUnread });
        dispatch({ type: "SET_LAST_POLLING_TIME", payload: new Date() });

        previousUnreadCountRef.current = totalUnread;
      } else {
        dispatch({ type: "SET_ROOMS", payload: [] });
        dispatch({ type: "SET_UNREAD_MESSAGES", payload: 0 });
        previousUnreadCountRef.current = 0;
      }
    });

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      unsubscribeRooms();
      document.removeEventListener("visibilitychange", handleVisibilityChange);

      // Cleanup room subscriptions
      for (const unsubscribe of roomSubscriptions.current.values()) {
        unsubscribe();
      }
      roomSubscriptions.current.clear();

      // Cleanup chat event listener
      chatEventListener.current.clearAll();
    };
  }, [authState.user, authState.isAuthenticated]);

  // Memoized provider value to prevent unnecessary re-renders
  const providerValue = useMemo(
    () => ({
      state,
      dispatch,
      getRooms,
      markMessagesAsRead,

      // Enhanced pub/sub functions
      subscribeToRoomMessages,
      publishRoomMessages,
      getRoomMessages,
    }),
    [
      state,
      getRooms,
      markMessagesAsRead,
      subscribeToRoomMessages,
      publishRoomMessages,
      getRoomMessages,
    ]
  );

  return (
    <SupportChatContext.Provider value={providerValue}>
      {children}
    </SupportChatContext.Provider>
  );
};

// Custom hook to use the support chat context
export const useSupportChat = () => {
  const context = useContext(SupportChatContext);
  if (!context) {
    throw new Error("useSupportChat must be used within a SupportChatProvider");
  }
  return context;
};
