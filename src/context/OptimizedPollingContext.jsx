import {
  createContext,
  useContext,
  useEffect,
  useRef,
  useCallback,
} from "react";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Context/Auth";
import { EventListener } from "Utils/EventListener";
import { useAuth } from "Context/Auth";

const OptimizedPollingContext = createContext();

export const OptimizedPollingProvider = ({ children }) => {
  const pollingRef = useRef(null);
  const eventListener = useRef(new EventListener({ name: "OptimizedPolling" }));
  const lastData = useRef(new Map()); // Map of eventType -> last data
  const isPollingActive = useRef(false);
  const authDispatchRef = useRef(null);
  const userIdRef = useRef(null);
  const { state: authState } = useAuth();

  const sdk = new MkdSDK();

  // Define allowed roles for polling
  const ALLOWED_POLLING_ROLES = ["admin_staff", "admin", "user"];

  // Check if current user role is allowed for polling
  const isRoleAllowedForPolling = useCallback(() => {
    return (
      authState.isAuthenticated &&
      authState.role &&
      ALLOWED_POLLING_ROLES.includes(authState.role)
    );
  }, [authState.isAuthenticated, authState.role]);

  // Function to play notification sound
  const playNotificationSound = useCallback(() => {
    try {
      const audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.3
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.log("Could not play notification sound:", error);
    }
  }, []);

  // Function to show push notification
  const showPushNotification = useCallback(async (messageData) => {
    try {
      // Check if notifications are supported and permitted
      if (
        !("Notification" in window) ||
        Notification.permission !== "granted"
      ) {
        return;
      }

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready;

      // Prepare notification data
      const notificationData = {
        title: "New Message - Court Matchup",
        body: messageData.message || "You have a new message",
        icon: "/courtmatchup-logo.png",
        badge: "/courtmatchup-logo.png",
        tag: "courtmatchup-message",
        requireInteraction: false,
        actions: [
          {
            action: "open",
            title: "Open Chat",
            icon: "/courtmatchup-logo.png",
          },
          {
            action: "close",
            title: "Close",
            icon: "/courtmatchup-logo.png",
          },
        ],
        data: {
          url: "/admin/support-chat",
          messageData: messageData,
          timestamp: Date.now(),
        },
      };

      // Show notification via service worker
      await registration.showNotification(
        notificationData.title,
        notificationData
      );

      console.log("📱 Push notification shown");
    } catch (error) {
      console.error("❌ Failed to show push notification:", error);
    }
  }, []);

  // Central polling function
  const pollForUpdates = useCallback(async () => {
    if (
      isPollingActive.current ||
      !userIdRef.current ||
      !isRoleAllowedForPolling()
    ) {
      return;
    }

    try {
      isPollingActive.current = true;
      const poll = await sdk.startPooling(userIdRef.current);

      if (poll.message) {
        console.log(
          "📨 New message notification received, notifying subscribers"
        );

        // Trigger message event using EventListener
        eventListener.current.trigger("message", poll);

        // Play notification sound
        playNotificationSound();

        // Show push notification if supported and page is not visible
        if (
          document.visibilityState === "hidden" &&
          "serviceWorker" in navigator
        ) {
          showPushNotification(poll);
        }
      }

      // Check for room updates only if there are room listeners
      if (eventListener.current.hasListeners("rooms")) {
        try {
          const roomsResponse = await sdk.getMyRoom();
          const currentRoomsData = JSON.stringify(roomsResponse);
          const lastRoomsData = lastData.current.get("rooms");

          if (currentRoomsData !== lastRoomsData) {
            lastData.current.set("rooms", currentRoomsData);

            // Trigger rooms event using EventListener
            eventListener.current.trigger("rooms", roomsResponse);
          }
        } catch (error) {
          console.error("Error fetching rooms:", error);
        }
      }
    } catch (error) {
      console.error("Polling error:", error);
      if (authDispatchRef.current) {
        tokenExpireError(authDispatchRef.current, error.message);
      }
      if (error.message === "TOKEN_EXPIRED") {
        window.location.replace("/user/login/");
      }
    } finally {
      isPollingActive.current = false;
    }
  }, [playNotificationSound, showPushNotification]);

  // Start polling when first subscriber joins
  const startPolling = useCallback(() => {
    if (pollingRef.current || !userIdRef.current || !isRoleAllowedForPolling())
      return;

    console.log("🚀 Starting optimized polling");
    pollingRef.current = setInterval(pollForUpdates, 30000); // Poll every 30 seconds
  }, [pollForUpdates, isRoleAllowedForPolling]);

  // Stop polling when no subscribers
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      console.log("🛑 Stopping optimized polling");
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Subscribe to specific event types
  const subscribe = useCallback(
    (eventType, callback) => {
      // Check if role is allowed for polling
      if (!isRoleAllowedForPolling()) {
        console.log(
          `❌ Polling not allowed for role: ${authState.role || "none"}`
        );
        // Return a no-op unsubscribe function
        return () => {};
      }

      // Add listener using EventListener
      eventListener.current.on(eventType, callback);
      console.log(
        `📝 Subscribed to ${eventType}, total listeners: ${
          eventListener.current.getEventListener(eventType)?.length || 0
        }`
      );

      // Start polling when first subscriber joins
      const totalSubscribers = eventListener.current.getTotalListenerCount();
      console.log("totalSubscribers", totalSubscribers);
      if (totalSubscribers === 1) {
        startPolling();
      }

      // Return unsubscribe function
      return () => {
        eventListener.current.off(eventType, callback);
        console.log(
          `📝 Unsubscribed from ${eventType}, remaining listeners: ${
            eventListener.current.getEventListener(eventType)?.length || 0
          }`
        );

        // Stop polling when no subscribers remain
        const totalSubscribers = eventListener.current.getTotalListenerCount();

        if (totalSubscribers === 0) {
          stopPolling();
        }
      };
    },
    [startPolling, stopPolling, isRoleAllowedForPolling, authState.role]
  );

  // Publish events manually (for testing or manual triggers)
  const publish = useCallback((eventType, data) => {
    eventListener.current.trigger(eventType, data);
  }, []);

  // Set user ID and auth dispatch for polling
  const setPollingConfig = useCallback(
    (userId, authDispatch) => {
      const previousUserId = userIdRef.current;
      userIdRef.current = userId;
      authDispatchRef.current = authDispatch;

      console.log("🔧 Polling config set for user:", userId);

      // If user changed or logged out, restart polling if there are subscribers
      if (previousUserId !== userId) {
        if (!userId) {
          // User logged out - stop polling immediately
          console.log("🚪 User logged out - stopping polling");
          stopPolling();
          lastData.current.clear(); // Clear cached data
        } else if (
          pollingRef.current &&
          eventListener.current.getTotalListenerCount() > 0 &&
          isRoleAllowedForPolling()
        ) {
          // User changed - restart polling only if role is allowed
          console.log("👤 User changed - restarting polling");
          stopPolling();
          startPolling();
        } else if (!isRoleAllowedForPolling() && pollingRef.current) {
          // Stop polling if new user role is not allowed
          console.log(
            `🚫 New user role ${
              authState.role || "none"
            } not allowed - stopping polling`
          );
          stopPolling();
          lastData.current.clear();
        }
      }
    },
    [stopPolling, startPolling, isRoleAllowedForPolling, authState.role]
  );

  // Manual trigger for immediate polling (useful for testing or force refresh)
  const triggerPoll = useCallback(() => {
    if (!isPollingActive.current) {
      pollForUpdates();
    }
  }, [pollForUpdates]);

  // Handle role changes - stop polling if role is not allowed
  useEffect(() => {
    if (!isRoleAllowedForPolling() && pollingRef.current) {
      console.log(
        `🚫 Role ${
          authState.role || "none"
        } not allowed for polling - stopping polling`
      );
      stopPolling();
      lastData.current.clear();
    }
  }, [
    authState.role,
    authState.isAuthenticated,
    isRoleAllowedForPolling,
    stopPolling,
  ]);

  // Listen for session expiry to stop polling
  useEffect(() => {
    const handleSessionExpired = () => {
      console.log("🚨 Session expired - stopping polling and clearing data");
      stopPolling();
      lastData.current.clear();
      userIdRef.current = null;
      authDispatchRef.current = null;
    };

    window.addEventListener("sessionExpired", handleSessionExpired);

    return () => {
      window.removeEventListener("sessionExpired", handleSessionExpired);
    };
  }, [stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
      eventListener.current.clearAll();
    };
  }, [stopPolling]);

  const value = {
    subscribe,
    publish,
    setPollingConfig,
    triggerPoll,
    playNotificationSound,
    isPolling: () => !!pollingRef.current,
    getSubscriberCount: (eventType) =>
      eventListener.current.getEventListener(eventType)?.length || 0,
    getTotalSubscribers: () => eventListener.current.getTotalListenerCount(),
    getDebugInfo: () => eventListener.current.getDebugInfo(),
    // EventListener methods for advanced usage
    scheduleEvent: (eventName, delay, data) =>
      eventListener.current.schedule(eventName, delay, data),
    scheduleRecurringEvent: (eventName, interval, data) =>
      eventListener.current.scheduleRecurring(eventName, interval, data),
    cancelScheduledEvent: (eventName) =>
      eventListener.current.cancelScheduled(eventName),
  };

  return (
    <OptimizedPollingContext.Provider value={value}>
      {children}
    </OptimizedPollingContext.Provider>
  );
};

// Custom hook to use the optimized polling context
export const useOptimizedPolling = () => {
  const context = useContext(OptimizedPollingContext);
  if (!context) {
    throw new Error(
      "useOptimizedPolling must be used within an OptimizedPollingProvider"
    );
  }
  return context;
};
