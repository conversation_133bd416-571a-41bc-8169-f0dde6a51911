import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{S as d}from"./index-6cd5ea29.js";const p=({price:l,isCurrentPlan:c,features:t,onSelect:m,popular:j,plan_name:x,isActive:s,advance_booking_days:e})=>r.jsxs("div",{className:`h-fit rounded-xl border border-gray-200 bg-white p-5 shadow-5 ${s?"border-primaryBlue":""}`,children:[r.jsxs("div",{className:" mb-3 flex items-center justify-between border-b border-gray-100 pb-4",children:[r.jsx("h3",{className:"text-lg font-semibold",children:x}),s&&r.jsx("span",{className:"rounded-lg bg-primaryBlue px-3 py-1 text-sm text-white",children:"Active"})]}),r.jsx("div",{className:"mb-4 text-2xl font-bold",children:l===0?r.jsx("span",{className:"text-green-600",children:"Free"}):r.jsxs(r.Fragment,{children:[d(l),r.jsx("span",{className:"text-sm font-normal text-gray-500",children:"/month"})]})}),r.jsxs("div",{className:"mb-4 rounded-lg bg-gray-50 p-3",children:[r.jsx("h6",{className:"mb-2 font-medium",children:"Advance Booking Days:"}),r.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[r.jsxs("div",{children:["Court: ",(e==null?void 0:e.court)||10," days"]}),r.jsxs("div",{children:["Lesson: ",(e==null?void 0:e.lesson)||10," days"]}),r.jsxs("div",{children:["Clinic: ",(e==null?void 0:e.clinic)||10," days"]}),r.jsxs("div",{children:["Buddy: ",(e==null?void 0:e.buddy)||10," days"]})]})]}),r.jsx("div",{className:"mb-4 space-y-5",children:t&&t.map((i,h)=>r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("svg",{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M5.50065 4.54704C7.32378 6.10228 8.47982 8.41646 8.47982 11.0007C8.47982 13.5848 7.32378 15.899 5.50065 17.4543M5.50065 4.54704C3.67752 6.10228 2.52148 8.41646 2.52148 11.0007C2.52148 13.5848 3.67752 15.899 5.50065 17.4543M5.50065 4.54704C6.98129 3.28397 8.90193 2.52148 11.0007 2.52148C13.0994 2.52148 15.02 3.28397 16.5007 4.54704M5.50065 17.4543C6.98129 18.7173 8.90193 19.4798 11.0007 19.4798C13.0994 19.4798 15.02 18.7173 16.5007 17.4543M16.5007 4.54704C14.6775 6.10228 13.5215 8.41646 13.5215 11.0007C13.5215 13.5848 14.6775 15.899 16.5007 17.4543M16.5007 4.54704C18.3238 6.10228 19.4798 8.41646 19.4798 11.0007C19.4798 13.5848 18.3238 15.899 16.5007 17.4543",stroke:"#525866",strokeWidth:"1.5"})}),r.jsx("span",{children:i.text})]},h))}),r.jsx("button",{onClick:m,className:`w-full rounded-lg py-2 ${s?"cursor-not-allowed bg-gray-100 text-gray-400":"border border-primaryBlue text-primaryBlue hover:bg-primaryBlue hover:text-white"}`,disabled:s,children:s?"Active":"Select"})]}),C=p;export{C as M};
