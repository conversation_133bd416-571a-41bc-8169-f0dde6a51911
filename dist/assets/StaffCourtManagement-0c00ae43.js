import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{C as s}from"./CourtManagement-8d74461c.js";import"./vendor-851db8c1.js";import{u as e,aQ as a,M as c}from"./index-6cd5ea29.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./BottomDrawer-598ec255.js";import"./HistoryComponent-9fdeea32.js";import"./date-fns-cca0f4f7.js";import"./TimeSlotGrid-640f0461.js";import"./PencilIcon-35185602.js";import"./TrashIcon-7d213648.js";import"./@headlessui/react-a5400090.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new c;function V(){const{club:r,courts:m,sports:i,club_permissions:t,fetchClubData:p}=e();return o.jsxs("div",{className:"px-4 sm:px-6 lg:px-8",children:[t&&t.court_management&&o.jsx(s,{fetchSettings:p,club:r,courts:m,sports:i,edit_api:"/v3/api/custom/courtmatchup/staff/club/profile-edit"}),!t&&o.jsx(a,{message:"You don't have permission to access the court management"})]})}export{V as default};
