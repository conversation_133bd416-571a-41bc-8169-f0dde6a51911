import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a}from"./vendor-851db8c1.js";import{S as f}from"./react-select-c8303602.js";import{M as ve,G as Ne,ai as Ce,d as we,b as S}from"./index-6cd5ea29.js";import{S as Se}from"./SuccessModal-e9ef416e.js";let m=new ve;const Ee=({isOpen:M,onClose:E,members:o})=>{if(!M)return null;const[y,_]=a.useState([]),[P,ie]=a.useState(""),[k,R]=a.useState("schedule"),[re,I]=a.useState(!1),{dispatch:j}=a.useContext(Ne),[$,ne]=a.useState([]),[x,G]=a.useState(null),[O,B]=a.useState(!1),[c,u]=a.useState({subject:"",message:"",date:"",time:"",repeat:0,type:0,days:[],end_date:"",condition:{before:0,time:30,condition_type:0,time_type:1},attachments:[],players:[]}),[ce,q]=a.useState(!1),[D,oe]=a.useState([]),[v,H]=a.useState(null),[de,F]=a.useState(null),[ue,J]=a.useState([]),[me,W]=a.useState(!1),[d,N]=a.useState({gender:"",ntrp:"",country:"",city:""}),[g,Z]=a.useState("members"),[A,xe]=a.useState([]),[z,U]=a.useState([]),[K,Q]=a.useState([]),[V,X]=a.useState([]),[i,T]=a.useState({categories:[],subcategories:[],tags:[],sport_id:"",type:"",sub_type:""}),[L,Y]=a.useState([]),[ee,se]=a.useState(!1),ge=[{label:"M",number:1,value:"monday"},{label:"T",number:2,value:"tuesday"},{label:"W",number:3,value:"wednesday"},{label:"T",number:4,value:"thursday"},{label:"F",number:5,value:"friday"},{label:"S",number:6,value:"saturday"},{label:"S",number:0,value:"sunday"}],te=(()=>(g==="members"?o:L).filter(t=>{const l=`${t.first_name} ${t.last_name}`.toLowerCase().includes(P.toLowerCase()),r=!d.gender||(t.gender||"").toLowerCase().includes(d.gender.toLowerCase()),n=!d.ntrp||t.ntrp===d.ntrp,b=!d.country||(t.country||"").toLowerCase().includes(d.country.toLowerCase()),h=!d.city||(t.city||"").toLowerCase().includes(d.city.toLowerCase());return l&&r&&n&&b&&h}))(),he=s=>{u(t=>({...t,days:t.days.includes(s)?t.days.filter(l=>l!==s):[...t.days,s]}))};a.useEffect(()=>{let s=[];g==="members"?s=y.map(t=>{var l;return((l=o.find(r=>r.id===t))==null?void 0:l.email)||""}).filter(t=>t):g==="clinic_participants"&&(s=y.map(t=>{var l;return((l=L.find(r=>r.id===t))==null?void 0:l.email)||""}).filter(t=>t)),u(t=>({...t,players:s}))},[y,o,g,L]);const pe=async()=>{if(!x){S(j,"Please select an email template",3e3,"error");return}I(!0);try{const s={...c,subject:x.subject,message:x.html,date:c.date,repeat:parseInt(c.repeat),condition:{before:parseInt(c.condition.before),time:c.condition.time,condition_type:parseInt(c.condition.condition_type),time_type:parseInt(c.condition.time_type)}};console.log(s),await m.callRawAPI("/v3/api/custom/courtmatchup/club/reservations/scheduling",s,"POST"),S(j,"Email scheduled successfully",3e3,"success"),W(!0)}catch(s){S(j,s.message,3e3,"error")}finally{I(!1)}};a.useEffect(()=>{(async()=>{try{const l=await(await fetch("/countries.min.json")).json(),r=Object.keys(l).map(n=>({value:n,label:n,cities:l[n].map(b=>({value:b,label:b}))}));oe(r)}catch(t){console.error("Error loading countries:",t)}})()},[]),a.useEffect(()=>{if(v){const s=D.find(t=>t.value===v.value);J((s==null?void 0:s.cities)||[])}else J([])},[v,D]);const p={control:s=>({...s,minHeight:36,borderColor:"#e5e7eb",borderRadius:"0.5rem","&:hover":{borderColor:"#e5e7eb"}}),valueContainer:s=>({...s,padding:"0 5px"}),input:s=>({...s,margin:0,padding:0})},be=async()=>{se(!0);try{m.setTable("clinic_subscription");const s=await m.callRestAPI({join:["user|user_id","clinics|clinic_id"]},"GETALL");s&&s.list&&xe(s.list)}catch(s){console.error("Error fetching clinic subscriptions:",s),S(j,"Failed to load clinic data",3e3,"error")}finally{se(!1)}},fe=async()=>{var s;try{const t=(s=o==null?void 0:o[0])==null?void 0:s.club_id;if(!t)return;m.setTable("clinic_categories");const l=await m.callRestAPI({filter:[`club_id,eq,${t}`]},"GETALL");m.setTable("clinic_subcategories");const r=await m.callRestAPI({filter:[`club_id,eq,${t}`]},"GETALL");m.setTable("clinic_tags");const n=await m.callRestAPI({filter:[`club_id,eq,${t}`]},"GETALL");U(l.list||[]),Q(r.list||[]),X(n.list||[])}catch(t){console.error("Error fetching categories and tags:",t),U([]),Q([]),X([])}},ye=()=>{if(!A.length){Y([]);return}let s=A.filter(r=>{const n=r.clinics,b=r.user;if(!n||!b)return!1;if(i.categories.length>0){const h=n.categories?JSON.parse(n.categories):[];if(!i.categories.some(C=>h.some(w=>w.id===C.id)))return!1}if(i.subcategories.length>0){const h=n.subcategories?JSON.parse(n.subcategories):[];if(!i.subcategories.some(C=>h.some(w=>w.id===C.id)))return!1}if(i.tags.length>0){const h=n.tags?JSON.parse(n.tags):[];if(!i.tags.some(C=>h.some(w=>w.id===C.id)))return!1}return!(i.sport_id&&n.sport_id!==parseInt(i.sport_id)||i.type&&n.type!==i.type||i.sub_type&&n.sub_type!==i.sub_type)});const t=[],l=new Set;s.forEach(r=>{l.has(r.user.id)||(l.add(r.user.id),t.push(r.user))}),Y(t)},je=async()=>{var s;B(!0);try{const t=(s=o==null?void 0:o[0])==null?void 0:s.club_id;if(!t)return;m.setTable("email");const l=await m.callRestAPI({filter:[`club_id,eq,${t}`]},"GETALL");l&&l.list&&ne(l.list)}catch(t){console.error("Error fetching email templates:",t),S(j,"Failed to load email templates",3e3,"error")}finally{B(!1)}},ae=s=>{G(s),u(t=>({...t,subject:(s==null?void 0:s.subject)||"",message:(s==null?void 0:s.html)||""}))};return a.useEffect(()=>{(o==null?void 0:o.length)>0&&(be(),fe(),je())},[o]),a.useEffect(()=>{ye()},[A,i]),a.useEffect(()=>{M&&(G(null),u(s=>({...s,subject:"",message:""})))},[M]),e.jsxs("div",{className:"fixed inset-0 z-50 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:E}),e.jsx("div",{className:"fixed inset-x-0 bottom-0 z-10",children:e.jsx("div",{className:"flex  min-h-full items-end justify-center text-center sm:items-center ",children:e.jsxs("div",{className:"relative h-full max-h-[97vh] w-full transform overflow-hidden rounded-t-2xl bg-white text-left shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 px-8 py-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Create email template"}),e.jsx("button",{onClick:E,className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M15 5L5 15M5 5L15 15",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"grid max-h-[calc(90vh-80px)] grid-cols-1 gap-8 overflow-y-auto bg-gray-100 p-8 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6 rounded-xl bg-white",children:[e.jsx("div",{className:"border-b p-4",children:e.jsx("h4",{className:"font-medium",children:"Settings"})}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"flex gap-4 border-b border-gray-100 pb-4",children:[e.jsx("button",{onClick:()=>R("schedule"),className:`font-medium ${k==="schedule"?"text-[#1D275F]":"text-gray-500"}`,children:"Schedule"}),e.jsx("button",{onClick:()=>R("condition"),className:`font-medium ${k==="condition"?"text-[#1D275F]":"text-gray-500 "}`,children:"Condition"})]}),k==="schedule"&&e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-gray-600",children:"Date/time"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("input",{type:"date",value:c.date,onChange:s=>u(t=>({...t,date:s.target.value})),className:"rounded-lg border border-gray-200 px-3 py-2 text-sm"}),e.jsx("input",{type:"time",value:c.time,onChange:s=>u(t=>({...t,time:s.target.value})),className:"rounded-lg border border-gray-200 px-3 py-2 text-sm"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",checked:c.repeat===1,onChange:s=>u(t=>({...t,repeat:s.target.checked?1:0})),className:"peer sr-only"}),e.jsx("div",{className:"h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#1D275F] peer-checked:after:translate-x-full peer-checked:after:border-white"})]}),e.jsx("span",{className:"text-sm text-gray-600",children:"Repeat"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 1.5L14.25 4.75L10.75 8M13.25 16L9.75 19.25L13.25 22.5M10.75 19.25H14C18.0041 19.25 21.25 16.0041 21.25 12C21.25 9.81504 20.2834 7.85583 18.7546 6.52661M13.25 4.75H10C5.99593 4.75 2.75 7.99594 2.75 12C2.75 14.1854 3.71696 16.145 5.24638 17.4742",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{className:"text-sm text-gray-600",children:"Repeat every"}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{children:"1"}),e.jsx("option",{children:"2"}),e.jsx("option",{children:"3"})]}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{children:"week"}),e.jsx("option",{children:"month"})]})]}),e.jsx("div",{className:"flex gap-2",children:ge.map(s=>e.jsx("button",{onClick:()=>he(s.value),className:`h-8 w-8 rounded-full border border-gray-200 ${c.days.includes(s.value)?"bg-[#1D275F] text-white":"text-gray-500"}`,children:s.label},s.value))}),ce?e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-gray-600",children:"End date"}),e.jsx("input",{type:"date",value:c.end_date,onChange:s=>u(t=>({...t,end_date:s.target.value})),className:"block rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"}),e.jsx("button",{onClick:()=>{q(!1),u(s=>({...s,end_date:""}))},className:"text-sm text-red-500",children:"Remove end date"})]}):e.jsxs("button",{onClick:()=>q(!0),className:"inline-flex items-center gap-2 text-sm text-[#1D275F] underline",children:[e.jsx("span",{children:"+"}),"Add end date"]})]}),k==="condition"&&e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("select",{value:c.condition.time,onChange:s=>u(t=>({...t,condition:{...t.condition,time:parseInt(s.target.value)}})),className:"w-16 rounded-lg border border-gray-200 px-3 py-2 text-sm",children:[...Array(60)].map((s,t)=>e.jsx("option",{value:t+1,children:t+1},t))}),e.jsxs("select",{value:c.condition.time_type,onChange:s=>u(t=>({...t,condition:{...t.condition,time_type:parseInt(s.target.value)}})),className:"w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{value:0,children:"minutes"}),e.jsx("option",{value:1,children:"hours"}),e.jsx("option",{value:2,children:"days"}),e.jsx("option",{value:3,children:"weeks"})]}),e.jsxs("select",{className:"w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",value:c.condition.before,onChange:s=>u(t=>({...t,condition:{...t.condition,before:parseInt(s.target.value)}})),children:[e.jsx("option",{value:0,children:"before"}),e.jsx("option",{value:1,children:"after"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm text-gray-600",children:"Reservation type"}),e.jsxs("select",{value:c.condition.condition_type,onChange:s=>u(t=>({...t,condition:{...t.condition,condition_type:parseInt(s.target.value)}})),className:"block w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{value:0,children:"Reservation"}),e.jsx("option",{value:1,children:"Clinic"})]})]})]})})]}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-lg font-medium",children:"Recipients"}),e.jsx("button",{onClick:()=>{_((g==="members"?o:L).map(t=>t.id))},className:"text-sm text-blue-500",children:"Send to all"})]}),e.jsxs("div",{className:"flex gap-4 border-b border-gray-100 pb-4",children:[e.jsx("button",{onClick:()=>{Z("members"),_([])},className:`font-medium ${g==="members"?"text-[#1D275F]":"text-gray-500"}`,children:"All Members"}),e.jsx("button",{onClick:()=>{Z("clinic_participants"),_([])},className:`font-medium ${g==="clinic_participants"?"text-[#1D275F]":"text-gray-500"}`,children:"Clinic Participants"})]}),e.jsxs("div",{className:"flex w-full items-center gap-2 rounded-lg border border-gray-200 px-3 ",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})}),e.jsx("input",{type:"text",placeholder:"search by name",value:P,onChange:s=>ie(s.target.value),className:"h-full w-full border-none bg-transparent py-2 text-sm outline-none focus:border-none focus:!outline-none"})]}),g==="clinic_participants"&&e.jsxs("div",{className:"space-y-3 rounded-lg bg-gray-50 p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Filter by Clinic Criteria"}),ee&&e.jsx("div",{className:"text-center text-sm text-gray-500",children:"Loading clinic data..."}),!ee&&e.jsxs(e.Fragment,{children:[z.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-gray-600",children:"Categories"}),e.jsx(f,{isMulti:!0,value:i.categories,onChange:s=>T(t=>({...t,categories:s||[]})),options:z.map(s=>({value:s.id,label:s.name,id:s.id,name:s.name})),placeholder:"Select categories...",styles:p,className:"text-sm"})]}),K.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-gray-600",children:"Subcategories"}),e.jsx(f,{isMulti:!0,value:i.subcategories,onChange:s=>T(t=>({...t,subcategories:s||[]})),options:K.map(s=>({value:s.id,label:s.name,id:s.id,name:s.name,category_id:s.category_id})),placeholder:"Select subcategories...",styles:p,className:"text-sm"})]}),V.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-gray-600",children:"Tags"}),e.jsx(f,{isMulti:!0,value:i.tags,onChange:s=>T(t=>({...t,tags:s||[]})),options:V.map(s=>({value:s.id,label:s.name,id:s.id,name:s.name})),placeholder:"Select tags...",styles:p,className:"text-sm"})]}),(i.categories.length>0||i.subcategories.length>0||i.tags.length>0)&&e.jsx("button",{onClick:()=>{T({categories:[],subcategories:[],tags:[],sport_id:"",type:"",sub_type:""})},className:"text-sm text-blue-500",children:"Clear clinic filters"})]})]}),e.jsxs("div",{className:"flex w-full justify-between gap-2",children:[e.jsx("div",{className:"flex-1",children:e.jsx(f,{value:v,onChange:s=>{H(s),F(null),N(t=>({...t,country:(s==null?void 0:s.value)||"",city:""}))},options:D,placeholder:"Country",styles:p,className:"text-sm"})}),e.jsx("div",{className:"flex-1",children:e.jsx(f,{value:de,onChange:s=>{F(s),N(t=>({...t,city:(s==null?void 0:s.value)||""}))},options:ue,placeholder:"City",isDisabled:!v,styles:p,className:"text-sm"})}),e.jsxs("select",{className:"flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm",value:d.gender,onChange:s=>N(t=>({...t,gender:s.target.value})),children:[e.jsx("option",{value:"",children:"Gender"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"})]}),e.jsxs("select",{className:"flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm",value:d.ntrp,onChange:s=>N(t=>({...t,ntrp:s.target.value})),children:[e.jsx("option",{value:"",children:"NTRP"}),Ce.map(s=>e.jsx("option",{value:s.toString(),children:s},s))]})]}),(d.gender||d.ntrp||d.country||d.city)&&e.jsx("button",{onClick:()=>{N({gender:"",ntrp:"",country:"",city:""}),H(null),F(null)},className:"mt-2 text-sm text-blue-500",children:"Clear filters"}),e.jsxs("div",{className:"max-h-[300px] space-y-3 overflow-y-auto rounded-lg bg-gray-50 p-4",children:[te.map((s,t)=>e.jsxs("label",{className:"flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",checked:y.includes(s.id),onChange:()=>_(l=>l.includes(s.id)?l.filter(r=>r!==s.id):[...l,s.id]),className:"h-4 w-4 rounded border-gray-300 text-[#1D275F] focus:ring-[#1D275F]"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-100",children:e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:s.first_name,className:"h-full w-full object-cover"})}),e.jsxs("span",{className:"text-sm text-gray-700",children:[s.first_name," ",s.last_name]})]})]},t)),te.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No recipients found"})]})]}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsx("p",{className:"text-lg font-medium",children:"Attachment"}),e.jsxs("div",{className:"rounded-lg border border-dashed border-gray-200 p-4 text-center",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Choose a file or drag & drop it here."}),e.jsx("p",{className:"text-xs text-gray-400",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB."}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm",children:"Browse File"})]})]})]}),e.jsxs("div",{className:"h-fit space-y-6 rounded-xl bg-white",children:[e.jsx("div",{className:"border-b p-4",children:e.jsx("h4",{className:"font-medium",children:"Email"})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Email Template"}),e.jsx(f,{value:x?{value:x.id,label:x.subject}:null,onChange:s=>{if(s){const t=$.find(l=>l.id===s.value);ae(t)}else ae(null)},options:$.map(s=>({value:s.id,label:s.subject})),placeholder:O?"Loading templates...":"Select an email template...",isLoading:O,isClearable:!0,styles:p,className:"text-sm"})]}),x&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Subject Preview"}),e.jsx("div",{className:"w-full rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-700",children:x.subject})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Message Preview"}),e.jsx("div",{className:"max-h-[200px] w-full overflow-y-auto rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-sm",children:x.html?e.jsx("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:x.html}}):e.jsx("div",{className:"italic text-gray-500",children:"No message content available"})})]})]})]}),e.jsx(we,{onClick:pe,loading:re,className:"mx-4 mt-2 w-fit rounded-lg bg-[#1D275F] px-4 py-2 text-sm text-white",children:"Send"})]})]})]})]})})}),me&&e.jsx(Se,{onContinue:()=>{W(!1),E()},description:"Email successfully scheduled for "+y.length+" recipients",title:"Email scheduled!"})]})};export{Ee as E};
