import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{r as I}from"./vendor-851db8c1.js";import{d as nt}from"./index-6cd5ea29.js";import{M as xt}from"./react-tooltip-7a26650a.js";import{f as ht,g as gt}from"./date-fns-cca0f4f7.js";const yt=({selectedDate:p,timeRange:w,onTimeClick:H,onNext:U,nextButtonText:it="Next",startHour:z=8,endHour:q=24,interval:F=30,className:rt="",multipleSlots:M=!1,timeSlots:wt=[],onTimeSlotsChange:T,individualSelection:It=!1,isTimeSlotAvailable:Tt,clubTimes:$=[],isLoading:ot,coachAvailability:E=[],height:lt="h-fit",minBookingTime:B=30,enforceMinBookingTime:P=!1,availabilityData:h=null,loadingAvailability:at=!1})=>{var X;const[v,J]=I.useState([]),[m,g]=I.useState([]),[ct,R]=I.useState(350),mt=I.useRef(null),N=I.useCallback(()=>{const t=[];for(let e=z;e<=q;e++)for(let i=0;i<60;i+=F){const s=e===24?0:e,n=`${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`,a=s>=12?"PM":"AM",o=`${s===0?12:s>12?s-12:s}:${i.toString().padStart(2,"0")} ${a}`;t.push({time24:n,time12:o})}return t},[z,q,F]);I.useEffect(()=>{if(w&&w.length>0&&w[0].from&&w[0].until){const t=N(),e=w[0].from,i=w[0].until;if(!t.find(o=>o.time12===e))return;const n=t.findIndex(o=>o.time12===i);if(n===-1)return;const a=t.findIndex(o=>o.time12===e),l=[];for(let o=a;o<n;o++)l.push(t[o].time24);g(l)}else g([])},[w,N]),I.useEffect(()=>{const t=()=>{const e=window.innerHeight;e<600?R(200):e<800?R(300):R(350)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]);const V=t=>{if(!$||$.length===0)return!0;const[e,i]=t.split(":"),s=parseInt(e)*60+parseInt(i);return $.some(n=>{const[a,l]=n.from.split(":"),[o,c]=n.until.split(":"),f=parseInt(a)*60+parseInt(l),u=parseInt(o)*60+parseInt(c);return s>=f&&s<=u})},W=t=>v.some(e=>{const i=N(),s=i.findIndex(l=>l.time12===e.from),n=i.findIndex(l=>l.time12===e.until),a=i.findIndex(l=>l.time24===t);return a>=s&&a<=n}),Z=t=>{if(!p||!E||E.length===0)return!0;const e=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=E.find(n=>n.day===e);if(!i)return!1;const s=`${t}:00`;return i.timeslots.includes(s)},G=t=>{if(!p||!(h!=null&&h.unavailableTimeSlots))return!1;const e=p.toISOString().split("T")[0];return h.unavailableTimeSlots.some(i=>{if(i.date!==e)return!1;const[s,n]=i.start_time.split(":").map(Number),[a,l]=i.end_time.split(":").map(Number),[o,c]=t.split(":").map(Number),f=s*60+n,u=a*60+l,A=o*60+c;return A>=f&&A<u})},K=t=>{if(!p||!h)return!0;if(h.qualifying_courts&&h.qualifying_courts.length>0){const n=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase();return h.qualifying_courts.some(l=>{try{const o=l.availability?JSON.parse(l.availability):[];if(!o||o.length===0)return!0;const c=o.find(u=>u.day===n);if(!c||!c.timeslots)return!1;const f=`${t}:00`;return c.timeslots.includes(f)}catch(o){return console.warn(`Failed to parse availability for court ${l.id}:`,o),!0}})}if(!h.availability)return!0;const e=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=h.availability.find(n=>n.day===e);if(!i||!i.timeslots)return!1;const s=`${t}:00`;return i.timeslots.includes(s)},Q=t=>{if(!p||!gt(p))return!1;const e=new Date,[i,s]=t.split(":").map(Number),n=new Date(p);return n.setHours(i,s,0,0),e>n},dt=t=>{var u,A,Y,D;if(!K(t.time24)||!Z(t.time24)||!V(t.time24)||M&&W(t.time24)||Q(t.time24)||G(t.time24))return;const e=N(),i=e.findIndex(d=>d.time24===t.time24);if(m.includes(t.time24)){if(m.length>=2){const d=[...m].sort();if(t.time24===d[0]||t.time24===d[d.length-1]){const S=m.filter(x=>x!==t.time24);g(S);const y=[...S].sort(),j=(u=e.find(x=>x.time24===y[0]))==null?void 0:u.time12;let L;const O=e.findIndex(x=>x.time24===y[y.length-1]),b=e[O+1];L=(b==null?void 0:b.time12)||((A=e.find(x=>x.time24===y[y.length-1]))==null?void 0:A.time12),H({from:j,until:L})}}else m.length===1&&(g([]),H({from:"",until:""}));return}let s;if(m.length===0)s=[t.time24],g(s);else{const d=[...m].sort(),S=d[d.length-1],y=e.findIndex(j=>j.time24===S);Math.abs(i-y)===1?(s=[...m,t.time24],g(s)):(s=[t.time24],g(s))}const n=[...s].sort(),a=(Y=e.find(d=>d.time24===n[0]))==null?void 0:Y.time12;let l;const o=e.findIndex(d=>d.time24===n[n.length-1]),c=e[o+1];l=(c==null?void 0:c.time12)||((D=e.find(d=>d.time24===n[n.length-1]))==null?void 0:D.time12);const f={from:a,until:l};if(P){const[d,S]=n[0].split(":").map(Number),[y,j]=n[n.length-1].split(":").map(Number),L=d*60+S,b=y*60+j-L+30;if(b<B){const tt=Math.ceil((B-b)/30),x=[];for(let k=1;k<=tt;k++){const st=o+k;st<e.length&&x.push(e[st].time24)}s=[...n,...x],g(s);const et=e.findIndex(k=>k.time24===x[x.length-1]),_=e[et+1];l=(_==null?void 0:_.time12)||e[et].time12,f.until=l}}H(f)},ut=()=>{var c,f;if(m.length===0)return;const t=N(),e=[...m].sort(),i=(c=t.find(u=>u.time24===e[0]))==null?void 0:c.time12;let s;const n=t.findIndex(u=>u.time24===e[e.length-1]),a=t[n+1];s=(a==null?void 0:a.time12)||((f=t.find(u=>u.time24===e[e.length-1]))==null?void 0:f.time12);const l={from:i,until:s},o=[...v,l];J(o),g([]),T==null||T(o)},ft=t=>{const e=v.filter((i,s)=>s!==t);J(e),T==null||T(e)},pt=t=>m.includes(t),C=N();return r.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${rt} ${lt}`,children:[p&&r.jsx("p",{className:"text-center font-medium",children:ht(p,"EEEE, MMMM d, yyyy")}),P&&r.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",B," minutes"]}),at&&r.jsx("div",{className:"mb-3 mt-2 rounded-lg bg-yellow-50 p-3 text-center",children:r.jsxs("div",{className:"flex items-center justify-center gap-2",children:[r.jsxs("svg",{className:"h-4 w-4 animate-spin text-yellow-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{className:"text-sm text-yellow-700",children:"Loading available time slots..."})]})}),r.jsx("div",{ref:mt,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${ct}px`},children:C.map((t,e)=>{const i=V(t.time24),s=Z(t.time24),n=K(t.time24),a=M&&W(t.time24),l=Q(t.time24),o=G(t.time24),c=n&&s&&i&&!a&&!l&&!o,f=i?n?s?l?"Time has passed":o?"Time slot unavailable":"":"Coach not available":"Not available for selected sport/type":"Club Closed";return r.jsxs("button",{onClick:()=>dt(t),disabled:!c,"data-tooltip-id":`time-${e}`,"data-tooltip-content":f,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${c?pt(t.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[t.time12,!c&&r.jsx(xt,{id:`time-${e}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},t.time24)})}),m.length>0&&r.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[r.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[r.jsx("span",{className:"font-medium",children:"From: "}),r.jsx("span",{className:"text-primaryBlue",children:(X=C.find(t=>t.time24===m.sort()[0]))==null?void 0:X.time12}),r.jsx("span",{className:"font-medium",children:"Until: "}),r.jsx("span",{className:"text-primaryBlue",children:(()=>{var s;const t=[...m].sort(),e=C.findIndex(n=>n.time24===t[t.length-1]),i=C[e+1];return(i==null?void 0:i.time12)||((s=C.find(n=>n.time24===t[t.length-1]))==null?void 0:s.time12)})()})]}),M&&r.jsx(nt,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:ut,disabled:m.length===0,children:"Add Time Range"})]}),M&&v.length>0&&r.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[r.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),r.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:v.map((t,e)=>r.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[r.jsx("span",{className:"text-gray-500",children:"From"}),r.jsx("span",{children:t.from}),r.jsx("span",{children:"-"}),r.jsx("span",{className:"text-gray-500",children:"Until"}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:t.until}),r.jsx("button",{onClick:()=>ft(e),className:"text-primaryBlue hover:text-primaryBlue/80",children:r.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},e))})]}),U&&r.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:r.jsx(nt,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:U,disabled:M?v.length===0:m.length===0,loading:ot,children:it})})]})},Ct=yt;export{Ct as T};
