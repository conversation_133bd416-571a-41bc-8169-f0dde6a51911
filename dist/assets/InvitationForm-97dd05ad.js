import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as m}from"./vendor-851db8c1.js";import{u as g}from"./react-hook-form-687afde5.js";import{o as h}from"./yup-2824f222.js";import{c as f,a as y}from"./yup-54691517.js";import{G as v,M as j,b as l}from"./index-6cd5ea29.js";const S=f().shape({email:y().email("Invalid email").required("Email is required")}),F=({onClose:r,role:t,club_id:c})=>{const{dispatch:a}=m.useContext(v),[i,o]=m.useState(!1),d=new j,{register:u,handleSubmit:p,formState:{errors:n}}=g({resolver:h(S)}),x=async b=>{try{o(!0);const s=await d.callRawAPI("/v3/api/custom/courtmatchup/generate-registration-code",{email:b.email,role:t,club_id:c,expiry_hours:24},"POST");if(s.error)throw new Error(s.message);l(a,"Invitation sent successfully",3e3,"success"),r()}catch(s){console.error("Error sending invitation:",s),l(a,s.message||"Failed to send invitation",3e3,"error")}finally{o(!1)}};return e.jsxs("div",{className:"p-6",children:[e.jsxs("h3",{className:"mb-6 text-xl font-medium text-gray-900",children:["Invite ",t.charAt(0).toUpperCase()+t.slice(1)]}),e.jsxs("form",{onSubmit:p(x),className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Email Address"}),e.jsx("input",{type:"email",...u("email"),className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter email address"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.email.message})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx("button",{type:"button",onClick:r,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:i,className:"rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:i?"Sending...":"Send Invitation"})]})]})]})};export{F as I};
