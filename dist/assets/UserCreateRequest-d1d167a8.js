import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,f as $e,b as He}from"./vendor-851db8c1.js";import"./BottomDrawer-598ec255.js";import{M as Ie,T as Ye,G as Ve,A as Ge,J as qe,aP as C,ad as Ze,e as We,S as v,d as ae,b as F}from"./index-6cd5ea29.js";import"./SelectionOption-658322e6.js";import{B as Je}from"./BackButton-11ba52b2.js";import{T as Qe}from"./TimeSlots-8e862e39.js";import{A as Ke}from"./AddPlayers-fec681af.js";import{S as P}from"./SelectionOptionsCard-30c39f7f.js";import{C as Ue}from"./ChevronLeftIcon-e5eecf9c.js";import{C as ze}from"./ChevronRightIcon-efb4c46c.js";import{f as R}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";let L=new Ie,ne=new Ye;const Xt=({isOpen:Xe=!0,onClose:et=()=>{},surfaces:tt=[]})=>{var Q,K,U,z,X,ee;a.useState("selection");const[m,re]=a.useState(null),[ie,st]=a.useState(null),[le,at]=a.useState(null),[d,oe]=a.useState(null),[l,A]=a.useState(new Date);a.useState(null);const[b,ce]=a.useState([]),[de,me]=a.useState([]),[s,ue]=a.useState(null),[g,nt]=a.useState(0),[_,O]=a.useState(0),[B,$]=a.useState(!1),[u,rt]=a.useState({from:null,until:null}),[f,M]=a.useState("main"),[pe,it]=a.useState(""),[r,H]=a.useState([]),[p,he]=a.useState([]),[T,xe]=a.useState(!1),[I,ge]=a.useState(1),[fe,Y]=a.useState(!1),[ye,lt]=a.useState(null),[je,V]=a.useState([]),[G,q]=a.useState([]),[y,ve]=a.useState([]),[h,E]=a.useState(null),[N,S]=a.useState(null),{state:ot,dispatch:D}=a.useContext(Ve);a.useContext(Ge);const Z=$e(),Ne=localStorage.getItem("user"),[W,ct]=a.useState({number:"0089"}),Se=async()=>{try{const t=await ne.getOne("user",Ne,{}),i=await L.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${t.model.club_id}`,{},"GET");he(i.sports),ue(i.model),H(o=>[...o,t.model])}catch(t){console.error(t)}};console.log("selectedPlayers",r);const we=async()=>{try{const t=await ne.getList("user",{filter:["role,cs,user"]});ce(t.list)}catch(t){console.error(t)}},Ce=async()=>{try{const t=await L.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");me(t.groups)}catch(t){console.error(t)}};a.useEffect(()=>{(async()=>(Y(!0),await Se(),await we(),await Ce(),Y(!1)))()},[]),He.useEffect(()=>{qe({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,title:"Reserve Court"})},[s==null?void 0:s.club_logo]),a.useEffect(()=>{},[f]);const be=()=>{A(new Date(l.setMonth(l.getMonth()-1)))},_e=()=>{A(new Date(l.setMonth(l.getMonth()+1)))},Me=()=>{const t=new Date(l.getFullYear(),l.getMonth(),1),i=new Date(l.getFullYear(),l.getMonth()+1,0);let o=t.getDay();o=o===0?6:o-1;const c=i.getDate(),k=["M","T","W","T","F","S","S"],w=Array(o).fill(null),Re=Array.from({length:c},(n,x)=>x+1),Le=[...w,...Re],te=n=>!d||!n?!1:d.getDate()===n&&d.getMonth()===l.getMonth()&&d.getFullYear()===l.getFullYear(),Ae=n=>{const x=new Date;return n===x.getDate()&&l.getMonth()===x.getMonth()&&l.getFullYear()===x.getFullYear()},se=n=>{if(!n)return!1;const x=new Date,Be=new Date(l.getFullYear(),l.getMonth(),n);return x.setHours(0,0,0,0),Be<x},Oe=n=>{!n||se(n)||oe(new Date(l.getFullYear(),l.getMonth(),n))};return e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between rounded-lg bg-gray-50 p-2",children:[e.jsx(Ue,{className:"h-5 w-5 cursor-pointer text-gray-600 hover:text-gray-800",onClick:be}),e.jsxs("h2",{className:"text-lg font-medium",children:[l.toLocaleString("default",{month:"long"})," ",l.getFullYear()]}),e.jsx(ze,{className:"h-5 w-5 cursor-pointer text-gray-600 hover:text-gray-800",onClick:_e})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-2",children:[k.map(n=>e.jsx("div",{className:"text-center text-sm font-medium text-gray-500",children:n},n)),Le.map((n,x)=>e.jsx("div",{onClick:()=>Oe(n),className:`
                h-8 cursor-pointer rounded-lg p-1 text-center text-sm
                ${n?"hover:bg-gray-100":"cursor-default"}
                ${se(n)?"cursor-not-allowed opacity-50 hover:bg-transparent":""}
                ${te(n)?"bg-blue-600 text-white hover:bg-blue-700":""}
                ${Ae(n)&&!te(n)?"border border-blue-600":""}
              `,children:n},x))]})]})},j=p==null?void 0:p.find(t=>t.id===m);a.useEffect(()=>{j?(V(j.sport_types||[]),console.log("selectedSportData",j),E(null),S(null)):(V([]),E(null),S(null))},[m,j]),a.useEffect(()=>{if(h&&j){const t=j.sport_types.find(i=>i.type===h);q((t==null?void 0:t.subtype)||[]),S(null)}else q([]),S(null)},[h,j]);const J=t=>{const[i,o]=t.split(" ");let[c,k]=i.split(":");return c=parseInt(c),o==="PM"&&c<12&&(c+=12),o==="AM"&&c===12&&(c=0),`${c.toString().padStart(2,"0")}:${k}:00`};(Q=y[0])!=null&&Q.from&&J((K=y[0])==null?void 0:K.from),(U=y[y.length-1])!=null&&U.until&&J((z=y[y.length-1])==null?void 0:z.until);const Te=async()=>{if(!m||!ie||!d||!u.from||!u.until){F(D,"Please select all required fields",3e3,"error");return}$(!0);try{const t=new Date(d),i={sport_id:m,type:1,surface_id:le,date:R(t,"yyyy-MM-dd"),player_ids:r.map(w=>w.id),start_time:u.from,end_time:u.until,price:_,base_fee:g*((r==null?void 0:r.length)||1),club_fee:(s==null?void 0:s.club_fee)||0,service_fee:C(s==null?void 0:s.fee_settings,g*((r==null?void 0:r.length)||1)),buddy_request:T,players_needed:I,group_id:ye,payment:{card_number:W.number}},o={sport_id:m,type:1,date:d,start_time:u.from,end_time:u.until,duration:1,court_id:1,price:_,player_ids:r.map(w=>w.id),buddy_request:T?1:0,buddy_details:null,payment_status:0,payment_intent:null},c={sport_id:m,slots:[{start_time:"10:00",end_time:"12:00"},{start_time:"14:00",end_time:"16:00"}],ntrp:3.5,num_players:4,num_needed:2,type:1,need_coach:1,notes:"Looking for intermediate players for a doubles match.",date:"2024-12-30",start_time:"10:00",end_time:"16:00",player_ids:[101,102,103]};(await L.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",o,"POST")).error||(F(D,"Reservation created successfully",3e3,"success"),Z("/user/dashboard"))}catch(t){console.error(t),F(D,t.message||"Error creating reservation",3e3,"error")}finally{$(!1)}},De=t=>{ve([{from:t.from,until:t.until}])},ke=t=>{H(i=>i.some(c=>c.id===t.id)?i.filter(c=>c.id!==t.id):[...i,t])};b==null||b.filter(t=>`${t.first_name} ${t.last_name}`.toLowerCase().includes(pe.toLowerCase()));const Fe=()=>{u.from&&u.until?M("players"):F(D,"Please select a time slot",3e3,"error")};a.useEffect(()=>{if(g&&(r!=null&&r.length)){const t=g*(r==null?void 0:r.length),i=C(s==null?void 0:s.fee_settings,t),o=(s==null?void 0:s.club_fee)||0;O(t+i+o)}else{const t=C(s==null?void 0:s.fee_settings,g),i=(s==null?void 0:s.club_fee)||0;O(g+t+i)}},[g,r,s==null?void 0:s.fee_settings,s==null?void 0:s.club_fee]);const Ee=()=>{M("payment")};console.log("generateTimeSlots",Ze()),Math.round((new Date(`2000/01/01 ${u.until}`)-new Date(`2000/01/01 ${u.from}`))/1e3/60/60);const Pe=s!=null&&s.lesson_description?JSON.parse(s==null?void 0:s.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"",children:[fe&&e.jsx(We,{}),e.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[f==="main"&&e.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),f==="players"&&e.jsx("div",{className:" ",children:"Step 2 • Request details"}),f==="payment"&&e.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),e.jsxs("div",{className:"p-4",children:[e.jsx(Je,{onBack:()=>{f==="main"?Z(-1):M(f==="payment"?"players":"main")}}),f==="main"?e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit space-y-6 rounded-lg bg-white p-4 shadow-5",children:[e.jsx(P,{title:"Sports",options:p||[],selectedOption:m,onOptionSelect:re,emptyMessage:"No sports found",optionType:"sport"}),e.jsx(P,{title:"Type",options:je,selectedOption:h,onOptionSelect:E,showPlaceholder:!m,optionType:"type"}),h&&G.length>0&&e.jsx(P,{title:"Surface Type",options:G,selectedOption:N,onOptionSelect:S,showPlaceholder:!h,optionType:"subtype"})]}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:Me()}),d&&e.jsx(Qe,{selectedDate:d,timeRange:u,onTimeClick:De,onNext:Fe,nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",clubTimes:s!=null&&s.times?JSON.parse(s.times):[],multipleSlots:!0,isTimeSlotAvailable:t=>!0})]})})})}):f==="payment"?e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-2 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Summary"}),e.jsxs("div",{className:"divide-y p-4",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"SPORT"}),e.jsxs("div",{className:"text-sm",children:[m&&((X=p==null?void 0:p.find(t=>t.id===m))==null?void 0:X.name),h&&` • ${h}`,N&&` • (${N})`]})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),e.jsx("div",{className:"text-sm",children:d&&R(d,"MMMM d, yyyy")})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"TIME"}),e.jsx("div",{className:"text-sm",children:y.map((t,i)=>e.jsxs("p",{children:[t.from," - ",t.until]},i))})]})]})]})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:v((s==null?void 0:s.club_fee)||0)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:v(C(s==null?void 0:s.fee_settings,g*((r==null?void 0:r.length)||1)))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:v(_)})]}),e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 p-3",children:[e.jsxs("div",{className:"flex items-center gap-3 ",children:[e.jsx("span",{className:"text-gray-400",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M22 10H2M22 12V8.2C22 7.0799 22 6.51984 21.782 6.09202C21.5903 5.71569 21.2843 5.40973 20.908 5.21799C20.4802 5 19.9201 5 18.8 5H5.2C4.0799 5 3.51984 5 3.09202 5.21799C2.71569 5.40973 2.40973 5.71569 2.21799 6.09202C2 6.51984 2 7.0799 2 8.2V15.8C2 16.9201 2 17.4802 2.21799 17.908C2.40973 18.2843 2.71569 18.5903 3.09202 18.782C3.51984 19 4.07989 19 5.2 19H18.8C19.9201 19 20.4802 19 20.908 18.782C21.2843 18.5903 21.5903 18.2843 21.782 17.908C22 17.4802 22 16.9201 22 15.8V12ZM7 15H7.01M4 15H4.01",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("span",{children:["Credit card • ",W.number]})]}),e.jsx("button",{className:"rounded-lg border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"Change"})]}),e.jsx(ae,{loading:B,onClick:Te,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Pay now"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur erat nisi, porta a ipsum eu, accumsan dapibus enim. Donec ultrices congue libero in convallis. Cras condimentum felis eget dignissim tincidunt."})]})]})})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Summary"})}),e.jsxs("div",{className:"divide-y p-4",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"SPORT"}),e.jsxs("div",{className:"text-sm",children:[m&&((ee=p==null?void 0:p.find(t=>t.id===m))==null?void 0:ee.name),h&&` • ${h}`,N&&` • (${N})`]})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),e.jsx("div",{className:"text-sm",children:d&&R(d,"MMMM d, yyyy")})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"TIME"}),e.jsx("div",{className:"text-sm",children:y.map((t,i)=>e.jsxs("p",{children:[t.from," - ",t.until]},i))})]})]})]}),e.jsx(Ke,{players:b,groups:de,selectedPlayers:r,onPlayerToggle:ke,isFindBuddyEnabled:T,onFindBuddyToggle:()=>xe(!T),playersNeeded:I,onPlayersNeededChange:ge}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",r.length,")"]}),e.jsx("div",{className:"mt-1",children:r.map(t=>e.jsxs("div",{className:"text-sm",children:[t.first_name," ",t.last_name]},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Club Fee"}),e.jsx("span",{children:v(s==null?void 0:s.club_fee)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:v(C(s==null?void 0:s.fee_settings,g))})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:v(_)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(ae,{loading:B,onClick:Ee,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Next: Payment"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:Pe.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]})]})]})};export{Xt as default};
