import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as N,r as c,u as z,f as I,L as _}from"./vendor-851db8c1.js";import{u as R}from"./react-hook-form-687afde5.js";import{o as q}from"./yup-2824f222.js";import{c as D,a as L}from"./yup-54691517.js";import{M as $,A as H,G as K,e as T,d as V,b as E}from"./index-6cd5ea29.js";import{A as W}from"./AuthLayout-3c5ec849.js";import{B as J,a as Q}from"./index.esm-3f8dc7b8.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let S=new $;const Pe=()=>{const k=D({email:L().email().required(),password:L().required()}).required(),{dispatch:F}=N.useContext(H),{dispatch:d}=N.useContext(K),[B,a]=c.useState(!1),[o,M]=c.useState(!1),[A,m]=c.useState(!1),G=z(),r=new URLSearchParams(G.search);r.get("club_id");const x=r.get("club_id"),Z=r.get("redirect_uri"),O=I(),{register:p,handleSubmit:P,setError:h,formState:{errors:X}}=R({resolver:q(k)}),U=async t=>{var l,i,u,g,w,j,C,y;try{a(!0);const s=await S.login(t.email,t.password,"coach");if(!s.error)F({type:"LOGIN",payload:s}),E(d,"Succesfully Logged In",4e3,"success"),O(Z??"/coach/dashboard");else if(a(!1),s.validation){const b=Object.keys(s.validation);for(let n=0;n<b.length;n++){const v=b[n];h(v,{type:"manual",message:s.validation[v]})}}}catch(s){a(!1),E(d,(i=(l=s==null?void 0:s.response)==null?void 0:l.data)!=null&&i.message?(g=(u=s==null?void 0:s.response)==null?void 0:u.data)==null?void 0:g.message:s==null?void 0:s.message,4e3,"error"),console.log("Error",s),h("email",{type:"manual",message:(j=(w=s==null?void 0:s.response)==null?void 0:w.data)!=null&&j.message?(y=(C=s==null?void 0:s.response)==null?void 0:C.data)==null?void 0:y.message:s==null?void 0:s.message})}},f=async t=>{m(!0);let l="coach";const i=await S.oauthLoginApi(t,l);window.open(i,"_self"),m(!1)};return e.jsx(W,{children:e.jsx("div",{className:"flex flex-col bg-white",children:e.jsxs("div",{className:"flex  flex-col ",children:[A&&e.jsx(T,{}),e.jsx("main",{className:"flex flex-col",children:e.jsx("div",{className:"flex w-full pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("section",{className:"mt-0 flex w-full justify-center max-md:max-w-full",children:e.jsx("div",{className:"flex w-[553px] min-w-[240px] flex-col items-center pt-12",children:e.jsxs("div",{className:"flex w-[392px] max-w-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex w-full flex-col",children:[e.jsx("div",{className:"flex w-[74px] items-start gap-4 self-center overflow-hidden rounded-[96px] p-2",children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_139_24350)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_139_24350)"}),e.jsxs("g",{filter:"url(#filter0_d_139_24350)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M24.5 37C24.5 33.6024 25.8556 30.5213 28.0554 28.2682C30.817 30.187 32.625 33.3824 32.625 37C32.625 40.6176 30.817 43.813 28.0554 45.7318C25.8556 43.4787 24.5 40.3976 24.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M34.5 37C34.5 32.9105 32.5361 29.2796 29.5 26.9991C31.5892 25.4299 34.186 24.5 37 24.5C39.814 24.5 42.4109 25.4299 44.5 26.9991C41.4639 29.2796 39.5 32.9105 39.5 37C39.5 41.0895 41.4639 44.7204 44.5 47.0009C42.4109 48.5701 39.814 49.5 37 49.5C34.186 49.5 31.5892 48.5701 29.5 47.0009C32.5361 44.7204 34.5 41.0895 34.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M45.9446 28.2683C48.1444 30.5213 49.5 33.6024 49.5 37C49.5 40.3976 48.1444 43.4787 45.9446 45.7317C43.183 43.813 41.375 40.6176 41.375 37C41.375 33.3824 43.183 30.187 45.9446 28.2683Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_139_24350",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_139_24350"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_139_24350",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("h1",{className:"mt-4 w-full text-center text-2xl font-medium leading-none text-gray-950",children:"Welcome back"}),e.jsx("p",{className:"mt-2  text-center text-gray-500",children:"Login to your account"})]}),e.jsxs("div",{children:[e.jsxs("button",{onClick:()=>f("apple"),className:"mb-4 flex w-full items-center justify-center gap-2 rounded-xl border border-solid border-zinc-200 bg-white px-6 py-3",children:[e.jsx("div",{children:e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.5923 10.0523C14.6133 12.3223 16.5836 13.0777 16.6055 13.0873C16.5888 13.1406 16.2906 14.1638 15.5674 15.2207C14.9422 16.1345 14.2933 17.0449 13.2712 17.0638C12.2668 17.0823 11.9439 16.4682 10.7956 16.4682C9.64763 16.4682 9.28881 17.0449 8.33805 17.0823C7.35141 17.1197 6.60006 16.0942 5.9697 15.1838C4.68158 13.3215 3.69718 9.92136 5.01897 7.62624C5.67561 6.48647 6.84907 5.76472 8.12276 5.74621C9.09162 5.72773 10.0061 6.39803 10.5984 6.39803C11.1903 6.39803 12.3016 5.59194 13.4698 5.71032C13.9589 5.73068 15.3318 5.90788 16.2133 7.19823C16.1423 7.24226 14.5752 8.15453 14.5923 10.0523ZM12.7047 4.47826C13.2285 3.8442 13.581 2.96153 13.4848 2.08328C12.7298 2.11363 11.8168 2.58642 11.2752 3.22013C10.7899 3.78131 10.3648 4.67951 10.4795 5.54038C11.3211 5.60549 12.1808 5.11272 12.7047 4.47826Z",fill:"black"})})}),e.jsx("span",{className:"text-sm font-medium text-gray-950",children:"Continue with Apple"})]}),e.jsxs("button",{onClick:()=>f("google"),className:"flex w-full items-center justify-center gap-2 rounded-xl border border-solid border-zinc-200 bg-white px-6 py-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1531 8.63647V11.541H14.2718C14.091 12.4751 13.5482 13.2661 12.7342 13.7979L15.218 15.6866C16.6651 14.3775 17.5 12.4547 17.5 10.1706C17.5 9.63884 17.4513 9.12742 17.3609 8.63656L10.1531 8.63647Z",fill:"#4285F4"}),e.jsx("path",{d:"M3.32103 6.63879C2.79926 7.64785 2.50012 8.78651 2.50012 10.0001C2.50012 11.2138 2.79926 12.3524 3.32103 13.3615C3.32103 13.3682 5.86747 11.4251 5.86747 11.4251C5.71441 10.9751 5.62394 10.4978 5.62394 10.0001C5.62394 9.50226 5.71441 9.02501 5.86747 8.57501L3.32103 6.63879Z",fill:"#FBBC05"}),e.jsx("path",{d:"M10.153 5.48638C11.2801 5.48638 12.2819 5.86819 13.082 6.60457L15.2736 4.45685C13.9447 3.24323 12.2194 2.5 10.153 2.5C7.16135 2.5 4.5802 4.1841 3.32092 6.63866L5.86728 8.57504C6.47254 6.80228 8.1632 5.48638 10.153 5.48638Z",fill:"#EA4335"}),e.jsx("path",{d:"M5.86399 11.4276L5.30381 11.8479L3.32092 13.3615C4.5802 15.8092 7.1612 17.5001 10.1528 17.5001C12.2191 17.5001 13.9515 16.8319 15.2178 15.6865L12.734 13.7978C12.0522 14.2478 11.1825 14.5206 10.1528 14.5206C8.16304 14.5206 6.47245 13.2047 5.86712 11.4319L5.86399 11.4276Z",fill:"#34A853"})]})}),e.jsx("span",{className:"text-sm font-medium text-gray-950",children:"Continue with Google"})]}),e.jsxs("div",{className:"my-6 flex items-center justify-center",children:[e.jsx("div",{className:"h-[1px] flex-1 bg-zinc-200"}),e.jsx("span",{className:"mx-4 text-sm text-gray-500",children:"OR"}),e.jsx("div",{className:"h-[1px] flex-1 bg-zinc-200"})]})]}),e.jsxs("form",{className:"flex w-full flex-col gap-2",onSubmit:P(U),children:[e.jsxs("div",{className:" flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"email",children:"Email"}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5",id:"email",type:"email",placeholder:"Email",...p("email")})]}),e.jsxs("div",{className:"mt-6 flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"password",children:"Password"}),e.jsxs("div",{className:"mt-1 flex w-full items-center  justify-between  overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-1  pr-2.5",children:[e.jsx("input",{id:"password",type:o?"text":"password",className:"h-full w-full border-none outline-none focus:outline-none focus:ring-0",placeholder:"Password",...p("password")}),e.jsx("button",{type:"button",className:"text-gray-500",onClick:()=>M(!o),children:o?e.jsx(J,{className:"h-5 w-5"}):e.jsx(Q,{className:"h-5 w-5"})})]}),e.jsx(_,{to:"/coach/forgot",className:"mt-2 text-black underline",children:"Forgot password?"})]}),e.jsx(V,{type:"submit",loading:B,className:"mt-6 w-full gap-2.5 self-stretch overflow-hidden whitespace-nowrap rounded-xl bg-emerald-800 px-2.5 py-3 text-center text-lg font-medium leading-none tracking-tight text-white shadow-sm",children:"Login"})]}),e.jsx("div",{className:"mt-5 text-center",children:e.jsxs(_,{to:x?`/coach/signup?club_id=${x}`:"/coach/signup",className:"text-gray-500",children:["Not a member yet?"," ",e.jsx("span",{className:"text-black underline underline-offset-2",children:"Sign up"})]})})]})})})})})]})})})};export{Pe as default};
