import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as X,b as l}from"./vendor-851db8c1.js";import{R as ee,M as se,G as te,A as le,c as ae,D as re,t as oe,b as j}from"./index-6cd5ea29.js";import{P as ne}from"./index-eb1bc208.js";import ce from"./Skeleton-1e8bf077.js";import{E as ie}from"./EmailTemplateDrawer-53017cf3.js";import{H as I}from"./HistoryComponent-9fdeea32.js";import{D as de}from"./DataTable-8a547681.js";import{R as me}from"./react-quill-45ec928f.js";/* empty css                   */const ue=({isOpen:c,onClose:u,editingEmail:s,setEditingEmail:i,onSubmit:f,isSubmitting:d})=>{const[x,b]=X.useState("edit"),S={toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{list:"ordered"},{list:"bullet"}],[{align:[]}],["link"],["clean"]]},y=["header","bold","italic","underline","strike","color","background","list","bullet","align","link"],g=r=>{r.preventDefault(),f(r)};return e.jsx(ee,{isOpen:c,onClose:u,title:s?"Edit Email Template":"Add New Email Template",primaryButtonText:s?"Update Template":"Create Template",onPrimaryAction:g,submitting:d,children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex border-b border-gray-200",children:[e.jsx("button",{onClick:()=>b("edit"),className:`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${x==="edit"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:"Rich Editor"}),e.jsx("button",{onClick:()=>b("html"),className:`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${x==="html"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:"HTML"}),e.jsx("button",{onClick:()=>b("preview"),className:`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${x==="preview"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:"Preview"})]}),e.jsx("div",{className:"flex-1 overflow-hidden",children:x==="edit"?e.jsxs("form",{onSubmit:g,className:"flex h-full flex-col space-y-4 py-4 sm:space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),e.jsx("input",{type:"text",name:"subject",value:(s==null?void 0:s.subject)||"",onChange:r=>i({...s,subject:r.target.value}),className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Message"}),e.jsx("div",{className:"flex flex-1 flex-col",children:e.jsx(me,{theme:"snow",value:(s==null?void 0:s.html)||"",onChange:r=>i({...s,html:r}),modules:S,formats:y,placeholder:"Enter your email content here. Use the toolbar above for formatting.",style:{height:"300px",display:"flex",flexDirection:"column",border:"1px solid #d1d5db",borderRadius:"6px"},className:"flex-1"})})]}),e.jsx("div",{className:"mt-4 text-xs text-gray-500",children:e.jsx("p",{children:"💡 Tip: Use the formatting toolbar above to style your email content with bold, italic, colors, lists, and more!"})})]}):x==="html"?e.jsxs("form",{onSubmit:g,className:"flex h-full flex-col space-y-4 py-4 sm:space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),e.jsx("input",{type:"text",name:"subject",value:(s==null?void 0:s.subject)||"",onChange:r=>i({...s,subject:r.target.value}),className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"HTML Content"}),e.jsx("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:e.jsx("span",{children:"💻 Code Editor"})})]}),e.jsx("div",{className:"relative flex-1",children:e.jsx("textarea",{name:"html",value:(s==null?void 0:s.html)||"",onChange:r=>i({...s,html:r.target.value}),className:"block h-full w-full resize-none rounded-md border border-gray-300 bg-gray-50 px-3 py-2 font-mono text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:`Enter your HTML content here...

Example:
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h1 style="color: #333; text-align: center;">Welcome!</h1>
  <p style="color: #666; line-height: 1.6;">Your message content here...</p>
  <div style="text-align: center; margin: 20px 0;">
    <a href="#" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Call to Action</a>
  </div>
</div>`,required:!0,style:{minHeight:"300px",lineHeight:"1.5",tabSize:"2"}})})]}),e.jsxs("div",{className:"mt-4 text-xs text-gray-500",children:[e.jsx("p",{children:"💡 Tip: You can write custom HTML and CSS here for complete control over styling. Use inline styles for best email client compatibility."}),e.jsx("p",{className:"mt-1",children:'Example: <p style="color: #333; font-size: 16px;">Your content</p>'})]})]}):e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"Email Preview"}),e.jsxs("div",{className:"rounded-md border bg-gray-50 p-3",children:[e.jsx("div",{className:"mb-1 text-sm text-gray-600",children:"Subject:"}),e.jsx("div",{className:"font-medium text-gray-900",children:(s==null?void 0:s.subject)||"No subject"})]})]}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsx("div",{className:"mb-2 text-sm text-gray-600",children:"Message:"}),e.jsx("div",{className:"flex-1 overflow-auto rounded-md border bg-white",children:s!=null&&s.html?e.jsx("div",{className:"prose prose-sm max-w-none p-4",dangerouslySetInnerHTML:{__html:s.html}}):e.jsx("div",{className:"p-4 italic text-gray-500",children:"No message content to preview"})})]})]})})]})})};let n=new se;const xe=[{header:"Subject",accessor:"subject",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"",accessor:"actions"}],pe=({club:c})=>{const{dispatch:u}=l.useContext(te),{dispatch:s}=l.useContext(le),[i,f]=l.useState([]),[d,x]=l.useState(10),[b,S]=l.useState(0),[y,g]=l.useState(0),[r,O]=l.useState(!1),[_,$]=l.useState(!1),[D,C]=l.useState(!0),[z,L]=l.useState(!1),[B,k]=l.useState(null),[G,U]=l.useState([]),[q,v]=l.useState(!1),[o,w]=l.useState(null),[V,A]=l.useState(!1),[W,T]=l.useState(!1),[Y,R]=l.useState(!1);function E(){p(y-1,d)}function F(){p(y+1,d)}async function p(t,a,m={},N=[]){C(!0);try{n.setTable("email");const h=await n.callRestAPI({payload:{...m},page:t,limit:a,filter:[...N,`club_id,eq,${c==null?void 0:c.id}`]},"PAGINATE");h&&C(!1);const{list:M,limit:J,num_pages:H,page:P}=h;f(M),x(J),S(H),g(P),O(P>1),$(P+1<=H)}catch(h){C(!1),console.log("ERROR",h),oe(s,h.message)}}const K=async()=>{try{n.setTable("user");const t=await n.callRestAPI({filter:["role,cs,user",`club_id,cs,${c==null?void 0:c.id}`]},"GETALL");n.setTable("profile");const a=await n.callRestAPI({},"GETALL"),m=t.list.map(N=>{const h=a.list.find(M=>M.user_id===N.id);return{...N,...h}});U(m)}catch(t){return console.error("Error fetching users:",t),[]}finally{}},Q=async t=>{t.preventDefault(),A(!0);try{if(o!=null&&o.id)n.setTable("email"),(await n.callRestAPI({subject:o.subject,html:o.html,id:o.id},"PUT")).error||(j(u,"Email template updated successfully!",3e3,"success"),f(i.map(m=>m.id===o.id?{...m,...o}:m)));else{n.setTable("email");const a=await n.callRestAPI({subject:o.subject,html:o.html,club_id:c.id},"POST");a.error||(j(u,"Email template created successfully!",3e3,"success"),f([{...o,id:a.data},...i]))}v(!1),w(null)}catch(a){console.error(a),j(u,"Error creating email template",3e3,"error")}finally{A(!1)}},Z=async t=>{R(!0);try{n.setTable("email"),(await n.callRestAPI({id:t},"DELETE")).error||(j(u,"Email template deleted successfully!",3e3,"success"),f(i.filter(m=>m.id!==t)))}catch(a){console.error(a),j(u,"An error occurred while deleting the template.",3e3,"error")}finally{R(!1),setShowDeleteModal(!1),k(null),T(!1)}};return l.useEffect(()=>{u({type:"SETPATH",payload:{path:"email"}}),K();const a=setTimeout(async()=>{await p(1,d)},700);return()=>{clearTimeout(a)}},[]),e.jsxs("div",{className:"h-screen px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 sm:gap-6 md:flex-row md:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-none flex-1 items-center justify-between sm:max-w-[300px] md:max-w-[350px] lg:max-w-[400px]",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(ae,{className:"h-4 w-4 text-gray-500 sm:h-5 sm:w-5"})}),e.jsx("input",{type:"text",className:"block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:py-2.5 sm:pl-11",placeholder:"Search email subject",onChange:t=>{t.target.value?p(0,d,{},[`subject,cs,${t.target.value}`]):p(0,d)}})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[e.jsxs("button",{onClick:()=>L(!0),className:"inline-flex items-center justify-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-4",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",children:e.jsx("path",{d:"M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"hidden sm:inline",children:"Send custom email"}),e.jsx("span",{className:"sm:hidden",children:"Send email"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsx(I,{title:"Email History",emptyMessage:"No email history found"})}),e.jsxs("button",{onClick:()=>{w(null),v(!0)},className:"inline-flex items-center justify-center gap-2 rounded-xl bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4",children:[e.jsx("span",{className:"text-sm",children:"+"}),e.jsx("span",{className:"hidden sm:inline",children:"Add new"}),e.jsx("span",{className:"sm:hidden",children:"Add"})]}),e.jsx("div",{className:"sm:hidden",children:e.jsx(I,{title:"Email History",emptyMessage:"No email history found"})})]})]}),D?e.jsx(ce,{}):e.jsx(de,{columns:xe,data:i,loading:D,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",onClick:t=>{w(t),v(!0)},renderCustomCell:{actions:t=>e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:a=>{a.stopPropagation(),k(t.id),T(!0)},className:"rounded-lg p-2 text-red-600 transition-colors duration-150 hover:bg-red-50 hover:text-red-800",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",children:e.jsx("path",{d:"M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})})},emptyMessage:"No email templates available",loadingMessage:"Loading email templates..."}),e.jsx(ne,{currentPage:y,pageCount:b,pageSize:d,canPreviousPage:r,canNextPage:_,updatePageSize:t=>{x(t),p(1,t)},previousPage:E,nextPage:F,gotoPage:t=>p(t,d)}),e.jsx(ie,{isOpen:z,onClose:()=>L(!1),members:G}),e.jsx(re,{isOpen:W,onClose:()=>{T(!1),k(null)},onDelete:()=>Z(B),loading:Y}),e.jsx(ue,{isOpen:q,onClose:()=>v(!1),editingEmail:o,setEditingEmail:w,onSubmit:Q,isSubmitting:V})]})},Ce=pe;export{Ce as L};
