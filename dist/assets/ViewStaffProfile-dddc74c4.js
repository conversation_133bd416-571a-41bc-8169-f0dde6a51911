import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{j as Y,r as m,b as Z}from"./vendor-851db8c1.js";import{G as q,e as Q,M as ee,T as se,_ as b,Z as j,$ as v,b as u,b0 as ae,d as L,W as te,X as re,S as le}from"./index-6cd5ea29.js";import"./BottomDrawer-598ec255.js";let n=new ee;const ie=new se;function me({club:o}){var I,$,U,A,D,k;const{id:g}=Y(),[e,y]=m.useState(null),{dispatch:d}=Z.useContext(q),[P,N]=m.useState(null),[h,x]=m.useState(""),[_,c]=m.useState(!1),[C,ne]=m.useState("personal"),[z,E]=m.useState(!0),[w,B]=m.useState(!1),[T,S]=m.useState(!1),G=localStorage.getItem("role"),f=async()=>{try{E(!0);const a=await ie.getOne("staff",g,{join:["user|user_id"]});y(a.model)}catch(a){console.error(a)}finally{E(!1)}},R=async a=>{var i;N(a);const r=["first_name","last_name","email","phone","role"].includes(a)?(i=e==null?void 0:e.user)==null?void 0:i[a]:e==null?void 0:e[a];x(r||"")},M=async a=>{var l,t;c(!0);try{const i=["first_name","last_name","email","phone","role"].includes(a);n.setTable(i?"user":"staff"),i?await n.callRestAPI({id:e.user.id,[a]:h},"PUT"):await n.callRestAPI({id:g,[a]:h},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,[a]:h},club_id:o==null?void 0:o.id,description:`Updated ${a} for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),await f(),N(null),u(d,"Updated successfully")}catch(r){console.error(r),u(d,"Error updating field","error")}finally{c(!1)}},F=()=>{N(null),x("")},V=async a=>{var l,t;try{c(!0);let r=new FormData;r.append("file",a);let i=await n.uploadImage(r);n.setTable("user"),await n.callRestAPI({id:g,photo:i==null?void 0:i.url},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,photo:null},club_id:o==null?void 0:o.id,description:`Modified profile picture for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),y({...e,photo:i==null?void 0:i.url}),u(d,"Photo updated successfully",3e3,"success")}catch(r){u(d,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{c(!1)}},H=async()=>{var a,l;try{c(!0),n.setTable("user"),await n.callRestAPI({id:g,photo:null},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,photo:null},club_id:o==null?void 0:o.id,description:`Removed photo for ${(a=e==null?void 0:e.user)==null?void 0:a.first_name} ${(l=e==null?void 0:e.user)==null?void 0:l.last_name}`}),y({...e,photo:null}),u(d,"Photo removed successfully",3e3,"success")}catch(t){u(d,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{c(!1)}};m.useEffect(()=>{f()},[g]);const p=(a,l,t,r=!1)=>s.jsx("div",{className:"border-b pb-4",children:P===a?s.jsxs("div",{children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("label",{className:"block text-gray-500",children:l}),s.jsx("button",{onClick:F,className:"text-primaryBlue hover:underline",children:"Cancel"})]}),a==="bio"?s.jsx("textarea",{value:h,onChange:i=>x(i.target.value),className:"mb-3 h-32 w-full rounded-lg border border-gray-300 p-2",placeholder:"Enter your bio..."}):a==="role"?s.jsxs("div",{className:"relative mb-3",children:[s.jsxs("div",{className:"flex w-full cursor-pointer items-center justify-between rounded-lg border border-gray-300 p-2",onClick:()=>S(!T),children:[s.jsx("span",{children:h||"Select role"}),s.jsx(ae,{className:"text-gray-500"})]}),T&&s.jsxs("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-gray-300 bg-white shadow-lg",children:[s.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-100",onClick:()=>{x("staff"),S(!1)},children:"staff"}),s.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-100",onClick:()=>{x("admin"),S(!1)},children:"admin"})]})]}):s.jsx("input",{type:"text",value:h,onChange:i=>x(i.target.value),className:"mb-3 w-full rounded-lg border border-gray-300 p-2"}),s.jsx(L,{loading:_,onClick:()=>M(a),className:"rounded-lg bg-[#1E335F] px-6 py-2 text-white hover:bg-[#162544]",children:"Save"})]}):s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-gray-500",children:l}),s.jsx("div",{className:"font-medium",children:t}),r&&s.jsxs("p",{className:"text-sm text-gray-400",children:["Your ",l.toLowerCase()," is not shared with other users."]})]}),(a!=="role"||["admin","club"].includes(G))&&s.jsx("button",{onClick:()=>R(a),className:"text-blue-600 hover:underline",children:"Edit"})]})}),O=async()=>{var a,l,t;try{c(!0);const r=((a=e==null?void 0:e.user)==null?void 0:a.status)===1?0:1;n.setTable("user"),await n.callRestAPI({id:e.user.id,status:r},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,status:r},club_id:o==null?void 0:o.id,description:`Changed status to ${r===1?"Active":"Inactive"} for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),y({...e,user:{...e.user,status:r}}),u(d,`Staff is now ${r===1?"Active":"Inactive"}`,3e3,"success")}catch(r){u(d,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{c(!1)}},K=async()=>{var a,l;try{c(!0);const t=parseFloat(h);if(isNaN(t))throw new Error("Please enter a valid number for pay rate");n.setTable("staff"),await n.callRestAPI({id:e.id,hourly_rate:t},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,hourly_rate:t},club_id:o==null?void 0:o.id,description:`Updated pay rate for ${(a=e==null?void 0:e.user)==null?void 0:a.first_name} ${(l=e==null?void 0:e.user)==null?void 0:l.last_name}`}),y({...e,hourly_rate:t}),N(null),u(d,"Pay rate updated successfully",3e3,"success")}catch(t){u(d,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{c(!1)}},J=()=>s.jsxs("div",{className:"mb-6 border-b pb-4",children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("h3",{className:"text-lg font-medium",children:"Pay Rate"}),s.jsx("button",{onClick:()=>B(!w),className:"flex items-center gap-1 text-blue-600 hover:underline",children:w?s.jsxs(s.Fragment,{children:[s.jsx(te,{size:16}),s.jsx("span",{children:"Hide Pay Rate"})]}):s.jsxs(s.Fragment,{children:[s.jsx(re,{size:16}),s.jsx("span",{children:"Show Pay Rate"})]})})]}),w&&s.jsx("div",{className:"mt-2",children:P==="hourly_rate"?s.jsxs("div",{children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("label",{className:"block text-gray-500",children:"Hourly Rate"}),s.jsx("button",{onClick:F,className:"text-primaryBlue hover:underline",children:"Cancel"})]}),s.jsxs("div",{className:"relative mb-3",children:[s.jsx("span",{className:"absolute left-3 top-2 text-gray-500",children:"$"}),s.jsx("input",{type:"number",step:"0.01",min:"0",value:h,onChange:a=>x(a.target.value),className:"mb-3 w-full rounded-lg border border-gray-300 p-2 pl-8",placeholder:"0.00"})]}),s.jsx(L,{loading:_,onClick:K,className:"rounded-lg bg-[#1E335F] px-6 py-2 text-white hover:bg-[#162544]",children:"Save"})]}):s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-gray-500",children:"Hourly Rate"}),s.jsx("div",{className:"font-medium",children:e!=null&&e.hourly_rate?le(e.hourly_rate):"Not set"})]}),s.jsx("button",{onClick:()=>{R("hourly_rate")},className:"text-blue-600 hover:underline",children:"Edit"})]})})]}),W=()=>{var a,l,t,r;return s.jsx("div",{className:"mb-6 border-b pb-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"mb-1 text-lg font-medium",children:"Staff Status"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`h-3 w-3 rounded-full ${((a=e==null?void 0:e.user)==null?void 0:a.status)===1?"bg-green-500":"bg-red-500"}`}),s.jsx("span",{className:"font-medium",children:((l=e==null?void 0:e.user)==null?void 0:l.status)===1?"Active":"Inactive"})]})]}),s.jsx("button",{onClick:O,disabled:_,className:`rounded-lg px-4 py-2 text-white ${((t=e==null?void 0:e.user)==null?void 0:t.status)===1?"bg-red-500 hover:bg-red-600":"bg-green-500 hover:bg-green-600"} disabled:opacity-50`,children:((r=e==null?void 0:e.user)==null?void 0:r.status)===1?"Set Inactive":"Set Active"})]})})},X=()=>{var a,l,t,r,i;switch(C){case"personal":return s.jsxs("div",{className:"space-y-4",children:[W(),J(),p("first_name","First Name",(a=e==null?void 0:e.user)==null?void 0:a.first_name),p("last_name","Last name",(l=e==null?void 0:e.user)==null?void 0:l.last_name),p("email","Email",(t=e==null?void 0:e.user)==null?void 0:t.email,!0),p("phone","Phone number",(r=e==null?void 0:e.user)==null?void 0:r.phone,!0),p("role","Role",((i=e==null?void 0:e.user)==null?void 0:i.role)||"staff"),p("bio","Bio",(e==null?void 0:e.bio)||"No bio provided")]});default:return null}};return s.jsxs("div",{children:[_||z?s.jsx(Q,{}):null,s.jsxs("div",{className:"mx-auto max-w-3xl",children:[s.jsx("h1",{className:"mb-6 text-2xl font-bold capitalize",children:!((I=e==null?void 0:e.user)!=null&&I.first_name)||!(($=e==null?void 0:e.user)!=null&&$.last_name)?"Staff's profile":`${(U=e==null?void 0:e.user)==null?void 0:U.first_name} ${(A=e==null?void 0:e.user)==null?void 0:A.last_name}'s profile`}),s.jsx("div",{className:"rounded-lg bg-white p-6 shadow",children:s.jsxs("div",{className:"space-y-6",children:[C==="personal"&&s.jsx("div",{className:"mb-6",children:s.jsxs("div",{className:"flex items-end gap-4",children:[s.jsx("img",{src:((D=e==null?void 0:e.user)==null?void 0:D.photo)||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"mb-1",children:"Upload Image"}),s.jsx("p",{className:"my-2 text-sm text-gray-600",children:"Min 400x400px, PNG or JPEG"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{disabled:!((k=e==null?void 0:e.user)!=null&&k.photo),className:"rounded-lg border border-red-600 px-2 py-1 text-red-600 disabled:opacity-50",onClick:H,children:"Remove"}),s.jsxs("label",{className:"cursor-pointer rounded-lg border border-gray-300 px-2 py-1 text-gray-500",children:[s.jsx("input",{type:"file",accept:".jpg,.jpeg,.png",className:"hidden",onChange:a=>{const l=a.target.files[0];l&&V(l)}}),"Change Photo"]})]})]})]})}),X()]})})]})]})}export{me as V};
