import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{b as n,r as t}from"./vendor-851db8c1.js";import{M as u,A as D,G as x,e as g}from"./index-6cd5ea29.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./react-tooltip-7a26650a.js";import{C as h}from"./ClubStats-431ec4a2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./HeatMapChart-da4e0846.js";import"./recharts-614cb831.js";import"./react-google-maps-db82edcf.js";const C=new u,mt=()=>{n.useContext(D),n.useContext(x);const[c,i]=t.useState(!1),[f,l]=t.useState([]),[d,E]=t.useState(new Date),[S,b]=t.useState(new Date),a=async(e=d,s=S)=>{try{i(!0);const o=e instanceof Date?e.toISOString().split("T")[0]:e,m=s instanceof Date?s.toISOString().split("T")[0]:s;console.log(o,m);const p=await C.callRawAPI(`/v3/api/custom/courtmatchup/staff/statistics?start_date=${o}&end_date=${m}&start_time=00:00&end_time=23:59`,{},"GET");p.error||l(p.model)}catch(o){console.log(o)}finally{i(!1)}};return t.useEffect(()=>{a()},[]),r.jsxs(r.Fragment,{children:[c&&r.jsx(g,{}),r.jsx(h,{stats:f,fetchDashboardStats:a})]})};export{mt as default};
