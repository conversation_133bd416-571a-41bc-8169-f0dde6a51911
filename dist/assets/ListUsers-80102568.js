import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as W,b as a}from"./vendor-851db8c1.js";import{M as X,G as ee,A as se,Z as j,t as te,d as ae,_ as re,$ as le,b as S}from"./index-6cd5ea29.js";import{P as oe}from"./index-eb1bc208.js";import{_ as P}from"./lodash-91d5d207.js";import ne from"./Skeleton-1e8bf077.js";import{H as E}from"./HistoryComponent-9fdeea32.js";import{D as ie}from"./DataTable-8a547681.js";import{F as ce}from"./FormattedPhoneNumber-40dd7178.js";let l=new X;const de=[{header:"Name",accessor:"name"},{header:"Email",accessor:"email"},{header:"Phone number",accessor:"phone_number"},{header:"Membership status",accessor:"slug"},{header:"NTRP",accessor:"ntrp"},{header:"",accessor:"actions"}],me=({club:o})=>{const Z=W(),{dispatch:$}=a.useContext(ee),{dispatch:h}=a.useContext(se),[H,k]=a.useState([]),[c,N]=a.useState(10),[v,V]=a.useState(0),[x,T]=a.useState(0),[A,D]=a.useState(!1),[R,F]=a.useState(!1),[t,u]=a.useState({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),[C,f]=a.useState(!0),[G,I]=a.useState(!1),w=a.useRef(null),[U,_]=a.useState(!1),[g,y]=a.useState(null),z=localStorage.getItem("user"),O=localStorage.getItem("role");function B(){d(x-1,c)}function q(){d(x+1,c)}async function d(s,r,p={},m=[]){f(!G),console.log("filters",m);try{l.setTable("profile");const i=await l.callRestAPI({payload:{...p},page:s,limit:r,filter:[...m,"role,cs,user",`club_id,eq,${o==null?void 0:o.id}`],join:["user|user_id"]},"PAGINATE");i&&f(!1);const{list:n,limit:Q,num_pages:M,page:b}=i;k(n),N(Q),V(M),T(b),D(b>1),F(b+1<=M)}catch(i){f(!1),console.log("ERROR",i),te(h,i.message)}}const K=()=>{const s=[];t.firstName&&s.push(`${l._project_id}_user.first_name,cs,${t.firstName}`),t.lastName&&s.push(`${l._project_id}_user.last_name,cs,${t.lastName}`),t.email&&s.push(`${l._project_id}_user.email,cs,${t.email}`),t.phone&&s.push(`${l._project_id}_user.phone,cs,${t.phone}`),t.ageGroup&&t.ageGroup!==""&&s.push(`${l._project_id}_age_group,cs,${t.ageGroup}`),t.status!==""&&s.push(`${l._project_id}_status,eq,${t.status}`),t.ntrp&&s.push(`ntrp,cs,${t.ntrp}`),t.membership&&s.push(`slug,cs,${t.membership}`),d(1,c,{},s)};a.useEffect(()=>{o!=null&&o.id&&d(1,c)},[o==null?void 0:o.id]),a.useEffect(()=>{$({type:"SETPATH",payload:{path:"users"}})},[]);const L=s=>{w.current&&!w.current.contains(s.target)&&setOpenFilter(!1)};a.useEffect(()=>(document.addEventListener("mousedown",L),()=>{document.removeEventListener("mousedown",L)}),[]);const Y={name:s=>{var r,p,m,i,n;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((r=s.user)==null?void 0:r.photo)||"/default-avatar.png",alt:`${(p=s.user)==null?void 0:p.first_name} ${(m=s.user)==null?void 0:m.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("span",{className:"font-medium text-gray-900",children:[P.truncate((i=s.user)==null?void 0:i.first_name,{length:15})," ",P.truncate((n=s.user)==null?void 0:n.last_name,{length:15})]})]})},slug:s=>e.jsx("span",{className:"text-gray-600",children:s.slug||"Member"}),ntrp:s=>e.jsx("span",{className:"text-gray-600",children:s.ntrp}),phone_number:s=>{var r;return e.jsx(ce,{phoneNumber:(r=s.user)==null?void 0:r.phone,className:"text-gray-600"})},email:s=>{var r;return e.jsx("span",{className:"text-gray-600",children:(r=s.user)==null?void 0:r.email})},actions:s=>e.jsx("div",{className:"flex items-center justify-end gap-3",children:e.jsx("button",{onClick:r=>{r.stopPropagation(),y(s),_(!0)},className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})})},J=({isOpen:s,onClose:r})=>{const[p,m]=a.useState(!1),i=async()=>{try{m(!0),l.setTable("profile");const n=await l.callRestAPI({id:Number(g.id)},"DELETE");l.setTable("user"),await l.callRestAPI({id:Number(g.user_id)},"DELETE"),await re(l,{user_id:z,activity_type:j.user_management,action_type:le.DELETE,data:g,club_id:o==null?void 0:o.id,description:"Deleted user"}),n.error||(S(h,"User deleted successfully",3e3,"success"),r(),y(null),d(x,c))}catch(n){S(h,n==null?void 0:n.message,3e3,"error"),console.log(n)}finally{m(!1)}};return e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${s?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-4 text-lg font-medium sm:text-xl",children:"Delete user"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600 sm:text-base",children:"Are you sure you want to delete this user?"}),e.jsxs("div",{className:"flex flex-col gap-2 border-t pt-4 sm:flex-row sm:justify-end sm:gap-3",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium hover:bg-gray-50",children:"Cancel"}),e.jsx(ae,{onClick:()=>{i()},className:"rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700",loading:p,children:"Yes, delete"})]})]})]})};return e.jsxs("div",{className:"h-full px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center",children:e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"User Filters"}),e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row",children:[e.jsxs("button",{onClick:()=>I(!0),className:"inline-flex items-center justify-center gap-2 rounded-lg bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4",children:[e.jsx("span",{className:"text-sm",children:"+"}),e.jsx("span",{className:"hidden sm:inline",children:"Add new"}),e.jsx("span",{className:"sm:hidden",children:"Add"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsx(E,{title:"User History",emptyMessage:"No user history found",activityType:j.user_management})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"Search by first name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.firstName,onChange:s=>{u({...t,firstName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Search by last name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.lastName,onChange:s=>{u({...t,lastName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"text",placeholder:"Search by email",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.email,onChange:s=>{u({...t,email:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"text",placeholder:"Search by phone",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.phone,onChange:s=>{u({...t,phone:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Status"}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.status,onChange:s=>{u({...t,status:s.target.value})},children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"NTRP"}),e.jsx("input",{type:"text",placeholder:"Search by NTRP",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:t.ntrp,onChange:s=>{u({...t,ntrp:s.target.value.trim()})}})]}),e.jsxs("div",{className:"col-span-full mt-4 flex flex-col gap-2 sm:mt-6 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{u({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),d(1,c)},className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Clear Filters"}),e.jsx("button",{onClick:()=>K(),className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Apply Filters"})]}),e.jsx("div",{className:"col-span-full sm:hidden",children:e.jsx(E,{title:"User History",emptyMessage:"No user history found",activityType:j.user_management})})]})]})}),C?e.jsx(ne,{}):e.jsx(e.Fragment,{children:e.jsx(ie,{columns:de,data:H,loading:C,renderCustomCell:Y,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No users available",loadingMessage:"Loading users...",onClick:s=>Z(`/${O}/view-user/${s.id}`)})}),v>0&&e.jsx(oe,{currentPage:x,pageCount:v,pageSize:c,canPreviousPage:A,canNextPage:R,updatePageSize:s=>{N(s),d(1,s)},previousPage:B,nextPage:q,gotoPage:s=>d(s,c)}),e.jsx(J,{isOpen:U,onClose:()=>{_(!1),y(null)},selectedUser:g})]})},Ne=me;export{Ne as L};
