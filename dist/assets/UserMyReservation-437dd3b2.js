import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,f as me,r as d}from"./vendor-851db8c1.js";import{M as fe,T as he,G as ge,u as xe,A as ve,ab as ye,aa as u,i as Se,a9 as G,S as be,t as je}from"./index-6cd5ea29.js";import{c as Ne,a as N}from"./yup-54691517.js";import{u as Ce}from"./react-hook-form-687afde5.js";import{o as _e}from"./yup-2824f222.js";import{P as Ee}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import{L as ke}from"./LoadingOverlay-87926629.js";import{G as Pe,L as Te,R as De,T as Le,P as Me,F as Re,C as Ie}from"./ReservationStatus-5ced670f.js";import{R as Ae}from"./index-c2f7d55c.js";import{S as Fe}from"./react-select-c8303602.js";import{f as $e}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@hookform/resolvers-67648cca.js";let g=new fe,D=new he;const O=[{header:"Date & Time",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Booking Type",accessor:"booking_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Court",accessor:"court_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"reservation_status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}}],Rs=()=>{const{dispatch:B,state:Ge}=t.useContext(ge),{courts:U}=xe(),{dispatch:z}=t.useContext(ve),[C,_]=t.useState([]),[x,Z]=t.useState(10),[K,Oe]=t.useState(0);t.useState(0);const[V,Be]=t.useState(0),[q,Ue]=t.useState(!1),[H,ze]=t.useState(!1),[Ze,J]=t.useState(!1);t.useState(!1),t.useState([]),t.useState([]),t.useState("eq");const[E,v]=t.useState(!0),[Q,Ke]=t.useState(!1),[W,Ve]=t.useState(!1);t.useState(),me();const L=t.useRef(null),[qe,X]=t.useState(!1),[M,k]=t.useState(null);t.useState([]),d.useState(!1);const[Y,w]=d.useState([]);d.useState([]),d.useState([]);const[p,ee]=d.useState("upcoming");d.useState(!1),d.useState(!1);const[se,te]=d.useState(null),[y,ae]=d.useState([]),[c,R]=d.useState(null),[I,ie]=d.useState(null),re=[{id:"upcoming",label:"Upcoming",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z",stroke:"black","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})},{id:"past",label:"Past",icon:()=>s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("rect",{width:"20",height:"20",fill:"white"}),s.jsx("path",{d:"M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:"cancelled",label:"Cancelled",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})}],ne=Ne({id:N(),email:N(),role:N(),status:N()});Ce({resolver:_e(ne)});function le(){f()}function oe(){f()}async function f(e,l,m={},o=[]){!W&&!Q&&v(!0),console.log("Selected family member:",c);try{let a;if(c&&c.value!=="all"&&c.value!=="me"?p==="past"||p==="cancelled"?a=await g.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${c.value.id}`,{},"GET"):a=await g.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${c.value.id}`,{},"GET"):p==="past"||p==="cancelled"?a=await g.callRawAPI("/v3/api/custom/courtmatchup/user/past-reservations",{},"GET"):a=await g.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET"),a){v(!1);const S=new Date;let r=a.list||a.reservations||[];if(console.log("Initial user reservations:",r.length),c&&c.value==="all"&&y.length>0)try{const i=localStorage.getItem("user"),h=y.filter(n=>n.id.toString()!==i);console.log(`Fetching reservations for ${h.length} family members`);const b=h.map(n=>p==="past"||p==="cancelled"?g.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${n.id}`,{},"GET"):g.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${n.id}`,{},"GET"));(await Promise.all(b)).forEach((n,T)=>{if(n&&(n.list||n.reservations)){const j=n.list||n.reservations||[];console.log(`Family member ${T+1} reservations:`,j.length),r=[...r,...j]}}),console.log("Total reservations before deduplication:",r.length),r=r.filter((n,T,j)=>T===j.findIndex(de=>de.reservation_id===n.reservation_id)),console.log("Combined reservations after deduplication:",r.length)}catch(i){console.error("Error fetching family reservations:",i)}p==="upcoming"?r=r.filter(i=>new Date(`${i.booking_date}T${i.start_time}`)>S&&i.booking_status!==u.CANCELLED&&(i.booking_status===u.PENDING||i.booking_status===u.SUCCESS)):p==="past"?r=r.filter(i=>new Date(`${i.booking_date}T${i.start_time}`)<=S&&i.booking_status!==u.CANCELLED&&(i.booking_status===u.PENDING||i.booking_status===u.SUCCESS||i.booking_status===u.FAIL)):p==="cancelled"&&(r=r.filter(i=>i.booking_status===u.CANCELLED)),_(r)}}catch(a){v(!1),console.log("ERROR",a),je(z,a.message)}}const ce=async()=>{try{const e=localStorage.getItem("user"),l=await D.getOne("user",e,{}),m=await g.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${l.model.club_id}`,{},"GET");w(m.sports),te(m.model)}catch(e){console.error(e)}},ue=async()=>{try{const e=localStorage.getItem("user"),l=parseInt(e);if(!e||isNaN(l)){console.error("Invalid user_id for fetching family members:",e);return}const m=await D.getList("user",{filter:[`guardian,eq,${l}`,"role,cs,user"]});ae(m.list)}catch(e){console.error("Error fetching family members:",e)}},pe=async()=>{try{const e=localStorage.getItem("user"),l=parseInt(e);if(!e||isNaN(l)){console.error("Invalid user_id for fetching user profile:",e);return}const m=await D.getOne("user",l,{});ie(m.model)}catch(e){console.error("Error fetching user profile:",e)}};t.useEffect(()=>{B({type:"SETPATH",payload:{path:"my-reservations"}});const l=setTimeout(async()=>{await f(1,x,{}),await ce(),await ue(),await pe()},700);return()=>{clearTimeout(l)}},[]),t.useEffect(()=>{_([]),v(!0),f(1,x,{})},[p]),t.useEffect(()=>{c!==null&&(_([]),v(!0),f(1,x,{}))},[c]),t.useEffect(()=>{c===null&&R({value:"all",label:"All Reservations"})},[y,I]);const A=e=>{L.current&&!L.current.contains(e.target)&&J(!1)};t.useEffect(()=>(document.addEventListener("mousedown",A),()=>{document.removeEventListener("mousedown",A)}),[]);const F=e=>{k(e),X(!0)};console.log("reservation data",C);const $=[{value:"all",label:"All Reservations"},{value:"me",label:"My Reservations"},...y.map(e=>({value:e,label:`${e.first_name} ${e.last_name} (${e.family_role||"Family Member"})`}))];return s.jsxs("div",{children:[s.jsxs("div",{className:"bg-white px-4 pt-4",children:[s.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My Reservations"}),(y.length>0||I)&&s.jsxs("div",{className:"mb-6 max-w-sm",children:[s.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Filter by family member"}),s.jsx(Fe,{className:"w-full text-sm",options:$,onChange:R,value:c,placeholder:"Select family member",isSearchable:!1,defaultValue:$[0]})]}),s.jsx("div",{className:"mb-0 flex max-w-fit  text-sm",children:re.map(e=>s.jsxs("button",{onClick:()=>ee(e.id),className:`flex items-center gap-2 bg-transparent px-3 py-3 ${p===e.id?"border-b-2 border-primaryBlue":""}`,children:[e.icon(),s.jsx("span",{className:"",children:e.label})]},e.id))})]}),s.jsxs("div",{className:"h-screen px-8",children:[E&&s.jsx(ke,{}),E?s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsxs("div",{className:"flex flex-col items-center gap-4",children:[s.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-primaryBlue"}),s.jsx("p",{className:"text-gray-500",children:"Loading reservations..."})]})}):s.jsxs("div",{className:"overflow-x-auto",children:[s.jsxs("table",{className:"w-full min-w-[1024px] ",children:[s.jsx("thead",{children:s.jsx("tr",{children:O.map((e,l)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:e.header},l))})}),s.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:C.map((e,l)=>{const m=ye(e==null?void 0:e.reservation_updated_at);return s.jsx("tr",{onClick:()=>F(e),className:"hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500",children:O.map((o,a)=>{var S,r,i,h,b,P;return o.accessor==""?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>F(e),children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})},a):o.accessor==="reservation_status"?s.jsxs("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:[(e==null?void 0:e.booking_type)=="Find Buddy"&&s.jsxs(s.Fragment,{children:[(e==null?void 0:e.num_needed)==(e==null?void 0:e.num_players)&&s.jsx(Pe,{title:"Group full"}),(e==null?void 0:e.num_needed)!=(e==null?void 0:e.num_players)&&s.jsx(Te,{numberOfBuddies:e==null?void 0:e.num_needed})]}),(e==null?void 0:e.booking_status)===u.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(De,{}),s.jsx(Le,{timeLeft:m})]})||(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx(Me,{})||(e==null?void 0:e.booking_status)===u.FAIL&&s.jsx(Re,{}),(e==null?void 0:e.booking_status)===u.CANCELLED&&s.jsx(Ie,{})]},a):o.accessor==="court_id"?s.jsx("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:((S=U.find(n=>n.id===(e==null?void 0:e.court_id)))==null?void 0:S.name)||"--"},a):o.mappingExist?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:o.mappings[e[o.accessor]]},a):o.accessor==="type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(r=Se.find(n=>n.value===(e==null?void 0:e.type)))==null?void 0:r.label},a):o.accessor==="date"?s.jsxs("td",{className:"whitespace-nowrap rounded-l-3xl px-6 py-4",children:[$e(new Date((e==null?void 0:e.booking_date)+"T00:00:00"),"MMMM d, yyyy")||"--"," "," | "," ",G((e==null?void 0:e.start_time)||"")||"--"," "," - "," ",G((e==null?void 0:e.end_time)||"")||"--"]},a):o.accessor==="booking_type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(e==null?void 0:e.booking_type)||"--"},a):o.accessor==="players"?s.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[e!=null&&e.player_ids?JSON.parse(e==null?void 0:e.player_ids).length:0," ","players"]},a):o.accessor==="price"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:be((e==null?void 0:e.price)||0)},a):o.accessor==="user"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((i=e==null?void 0:e.user)!=null&&i.first_name)||!((h=e==null?void 0:e.user)!=null&&h.last_name)?"--":`${(b=e==null?void 0:e.user)==null?void 0:b.first_name} ${(P=e==null?void 0:e.user)==null?void 0:P.last_name}`},a):s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:e[o.accessor]},a)})},l)})})]}),!E&&C.length===0&&s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"text-center",children:s.jsx("p",{className:"text-gray-500",children:"No reservations found for this tab"})})})]}),s.jsx(Ee,{currentPage:V,pageCount:K,pageSize:x,canPreviousPage:q,canNextPage:H,updatePageSize:e=>{Z(e),f()},previousPage:le,nextPage:oe,gotoPage:e=>f()}),s.jsx(Ae,{isOpen:!!M,onClose:()=>k(null),reservation:M,clubSports:Y,club:se,onReservationCanceled:()=>{f(1,x,{}),k(null)}})]})," "]})};export{Rs as default};
