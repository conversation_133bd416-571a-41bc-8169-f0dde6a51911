import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{a0 as o}from"./index-6cd5ea29.js";import{_ as a}from"./react-qr-code-4a7125ac.js";function c({onClose:t}){const s="https://play.google.com/store/apps/details?id=com.courtmatchup",l="https://apps.apple.com/app/courtmatchup";return e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"mx-4 w-full max-w-lg rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-medium",children:"Court Matchup mobile app"}),e.jsxs("p",{className:"mt-2 text-gray-600",children:["For better experience and convenience get our mobile app!",e.jsx("br",{}),"Scan the QR code or search"," ",e.jsx("span",{className:"font-semibold",children:"'Court Matchup'"})," on the App store"]})]}),e.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:e.jsx(o,{size:24})})]}),e.jsx("div",{className:"mt-6",children:e.jsx("div",{className:"h-[420px] rounded-lg bg-gray-100 bg-[url('/image/app-download.png')] bg-cover bg-left-top p-2",children:e.jsx("div",{className:"flex h-full flex-col items-end justify-center gap-8 md:flex-row",children:e.jsxs("div",{className:"flex w-full justify-center gap-3",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-lg bg-white bg-white/20 p-5 backdrop-blur-sm",children:[e.jsx("div",{className:"mb-3 rounded-xl bg-white p-3 shadow-lg",children:e.jsx(a,{value:s,size:128,style:{height:"auto",maxWidth:"100%",width:"100%"},viewBox:"0 0 128 128"})}),e.jsx("a",{href:s,target:"_blank",rel:"noopener noreferrer",className:"inline-block overflow-hidden rounded-lg bg-black",children:e.jsx("img",{src:"https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png",alt:"Get it on Google Play",className:"h-12"})})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-lg bg-white bg-white/20 p-5 backdrop-blur-sm",children:[e.jsx("div",{className:"mb-3 rounded-xl bg-white p-3 shadow-lg",children:e.jsx(a,{value:l,size:128,style:{height:"auto",maxWidth:"100%",width:"100%"},viewBox:"0 0 128 128"})}),e.jsx("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:"inline-block overflow-hidden rounded-lg bg-black",children:e.jsx("img",{src:"https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg",alt:"Download on the App Store",className:"h-12"})})]})]})})})})]})})}export{c as D};
