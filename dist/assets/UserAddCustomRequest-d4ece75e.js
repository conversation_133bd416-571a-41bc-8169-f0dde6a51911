import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,f as Ne,L as xe,b as Se}from"./vendor-851db8c1.js";import{ac as le,G as je,aP as he,c as be,S as k,d as _e,aR as Re,M as ve,b as G,aH as Ce,u as Pe,J as ke,e as Me,T as Be}from"./index-6cd5ea29.js";import{T as Te}from"./TimeSlots-8e862e39.js";import{S as Ae}from"./SportTypeSelection-5dc32d74.js";import{C as Ee}from"./Calendar-35bce269.js";import{c as Le}from"./index.esm-3f8dc7b8.js";import{A as $e}from"./AddPlayers-fec681af.js";import{B as Ie}from"./BottomDrawer-598ec255.js";import{B as De}from"./BackButton-11ba52b2.js";import{L as fe}from"./ReservationSummary-50e44b54.js";import{f as Fe}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./index.esm-09a3a6b8.js";function Oe({title:l,titleId:m,...d},c){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":m},d),l?r.createElement("title",{id:m},l):null,r.createElement("path",{fillRule:"evenodd",d:"M4.25 12a.75.75 0 0 1 .75-.75h14a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75Z",clipRule:"evenodd"}))}const qe=r.forwardRef(Oe),He=qe;function Ge({title:l,titleId:m,...d},c){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":m},d),l?r.createElement("title",{id:m},l):null,r.createElement("path",{fillRule:"evenodd",d:"M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))}const Ve=r.forwardRef(Ge),Ze=Ve,Je=new ve;function Ye({coaches:l,onClose:m,selectedDate:d,selectedSport:c,timeRange:w,sports:M,players:h,groups:B,club:a,isOpen:de,selectedType:N,selectedSubType:S,selectedTimes:f,userProfile:T,notes:g,numberOfPlayers:b}){const[j,V]=r.useState(!0),[t,Z]=r.useState(null),[A,_]=r.useState(""),[v,R]=r.useState(1),[u,E]=r.useState([]),[L,$]=r.useState(1),[I,D]=r.useState(!1),[J,Y]=r.useState(null),[U,F]=r.useState(!1),[i,z]=r.useState(0),[O,K]=r.useState(0),Q=Ne(),{duration:y,end_time:W,start_time:X}=le(f),[me,ce]=r.useState(null),[ee,ye]=r.useState(null),[se,ue]=r.useState(null),{dispatch:C}=r.useContext(je),[q,te]=r.useState(null),re=()=>{$(s=>Math.min(s+1,10))},pe=()=>{$(s=>Math.max(s-1,1))},ae=async()=>{if(!c||!d||!f||!u){G(C,"Please select all required fields",3e3,"error");return}F(!0);try{const s={sport_id:c,type:N,sub_type:S,date:d,player_ids:u.map(p=>p.id),start_time:X,end_time:W,duration:y,coach_id:t?t.id:null,payment_intent:null,custom_request:1,notes:g,num_needed:b,reservation_type:Ce.coach},o=await Je.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",s,"POST");o.error||(G(C,"Request created successfully",3e3,"success"),te(o.reservation_id),ue(o.booking_id),Q("/user/lessons"))}catch(s){console.error(s),G(C,s.message||"Error creating reservation",3e3,"error")}finally{F(!1)}},ne=s=>{E(o=>o.some(n=>n.id===s.id)?o.filter(n=>n.id!==s.id):[...o,s])},ie=()=>{t&&R(2)};r.useEffect(()=>{if(y&&(t!=null&&t.hourly_rate)){const s=y*(t==null?void 0:t.hourly_rate),o=he(a==null?void 0:a.fee_settings,s),p=(a==null?void 0:a.club_fee)||0;z(s+o+p),K(s)}},[y,t,a==null?void 0:a.fee_settings,a==null?void 0:a.club_fee]);const oe=a!=null&&a.lesson_description?JSON.parse(a==null?void 0:a.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Ie,{onClose:m,isOpen:de,title:v===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-6xl overflow-y-auto rounded-lg bg-white p-6",children:[e.jsx(De,{onBack:()=>{}}),v===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:A,onChange:s=>_(s.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(be,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>V(!j),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(Le,{className:`text-xs transition-transform ${j?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[l.length>0&&l.filter(s=>`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase().includes(A.toLowerCase())).sort((s,o)=>{const p=`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase(),n=`${o==null?void 0:o.first_name} ${o==null?void 0:o.last_name}`.toLowerCase();return j?p.localeCompare(n):n.localeCompare(p)}).map(s=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===s.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>Z(s),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[k(s.hourly_rate),"/h"]})]},s.id)),e.jsx("div",{className:"flex h-full flex-col items-center justify-center gap-4",children:e.jsx("button",{onClick:()=>{Z(null),R(2)},className:"b rounded-lg border border-primaryBlue px-4 py-2 text-sm text-primaryBlue ",children:"Continue without coach"})})]})]}),t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsxs("p",{className:"text-lg text-gray-600",children:[k(t==null?void 0:t.hourly_rate),"/h"]})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:ie,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})]}),v===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(fe,{selectedSport:c,sports:M,selectedType:N,selectedSubType:S,selectedDate:d,selectedTimes:f,playersNeeded:L,selectedCoach:t,timeRange:le(f)}),e.jsx($e,{searchQuery:A,setSearchQuery:_,selectedPlayers:u,setSelectedPlayers:E,onPlayerToggle:ne,players:h,groups:B,selectedGroup:J,isFindBuddyEnabled:I,setIsFindBuddyEnabled:D,playersNeeded:L,handleIncrement:re,handleDecrement:pe,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",u.length,")"]}),e.jsx("div",{className:"mt-1",children:u.map(s=>s.id===T.id?e.jsx("div",{className:"text-sm",children:"Me"},s.id):e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsx("div",{className:"text-sm",children:t?`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`:"No coach selected"})})]})]}),e.jsx(_e,{loading:U,onClick:ae,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Send request"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:oe.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),v===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(fe,{selectedSport:c,sports:M,selectedType:N,selectedSubType:S,selectedDate:d})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:k((a==null?void 0:a.club_fee)||0)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:k(he(a==null?void 0:a.fee_settings,O))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:k(i)})]}),e.jsx("div",{children:e.jsx(Re,{user:T,bookingId:se,clientSecret:me,paymentIntent:ee,reservationId:q,navigateRoute:`/user/payment-success/${q}?type=lesson`})}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(xe,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(xe,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let H=new ve,ge=new Be;function qs(){const[l,m]=r.useState(null),[d,c]=r.useState(null),[w,M]=r.useState(null),[h,B]=r.useState(new Date),[a,de]=r.useState([]),[N,S]=r.useState(null),[f,T]=r.useState(null),[g,b]=r.useState(5),[j,V]=r.useState(""),[t,Z]=r.useState(0),[A,_]=r.useState(0),[v,R]=r.useState(!1),[u,E]=r.useState([]),[L,$]=r.useState([]),[I,D]=r.useState(!1),[J,Y]=r.useState(!1),[U,F]=r.useState(null),[i,z]=r.useState(null),[O,K]=r.useState([]),[Q,y]=r.useState([]),[W,X]=r.useState([]),{state:me,dispatch:ce}=r.useContext(je),[ee,ye]=r.useState({from:null,until:null}),[se,ue]=r.useState("main"),{user_permissions:C,courts:q}=Pe(),{start_time:te,end_time:re,duration:pe}=le(u),ae=localStorage.getItem("user"),ne=async()=>{try{const n=await H.callRawAPI("/v2/api/lambda/preference",{},"GET"),x=await ge.getOne("user",ae,{}),P=await H.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${x.model.club_id}`,{},"GET"),we=await ge.getList("user",{filter:["role,cs,user",`club_id,cs,${P.model.id}`]});y(we.list),K(P.sports),F(n),z(P.model)}catch(n){console.error(n)}},ie=async()=>{try{const n=await H.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");X(n.groups)}catch(n){console.error(n)}};r.useEffect(()=>{(async()=>(Y(!0),await ne(),await ie(),Y(!1)))()},[]),r.useEffect(()=>{},[se]);const oe=()=>{B(new Date(h.setMonth(h.getMonth()-1)))},s=()=>{B(new Date(h.setMonth(h.getMonth()+1)))},o=n=>{E([{from:n.from,until:n.until}])};r.useEffect(()=>{t&&(a!=null&&a.length)?_(t*(a==null?void 0:a.length)):_(t)},[t,a]);const p=async()=>{R(!0);const n={start_time:te,sport_id:l,end_time:re,date:Fe(new Date(w),"yyyy-MM-dd")};try{const x=await H.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",n,"POST");x.error||($(x.list),D(!0))}catch(x){console.error("ERROR",x),G(ce,x.message,5e3)}finally{R(!1)}};return Se.useEffect(()=>{ke({path:"/user/create-custom-request",clubName:i==null?void 0:i.name,favicon:i==null?void 0:i.club_logo,title:"Custom request",description:"Custom Request"})},[i==null?void 0:i.club_logo]),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:e.jsx("p",{children:"Custom Request"})}),J&&e.jsx(Me,{}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"flex items-center justify-center rounded-lg bg-gray-50 p-3 text-center",children:"Summary"}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Number of players"}),e.jsxs("div",{className:"flex max-w-fit items-center gap-2 rounded-xl border border-gray-300",children:[e.jsx("button",{onClick:()=>b(g-1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(He,{className:"h-4 w-4"})}),e.jsx("input",{type:"number",className:"w-8 rounded-lg border-none p-0 text-center text-gray-700",value:g,min:1,onChange:n=>b(n.target.value)}),e.jsx("button",{onClick:()=>b(g+1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(Ze,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("p",{className:"text-sm text-gray-600",children:"Custom requests are meant for parties of 5 or move players. The requests made will be checked by the club and the club will respond to you over the registered email address."})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-900",children:["Custom request"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsx("textarea",{className:"w-full rounded-xl border border-gray-300 p-2 text-sm",placeholder:"Add a note",rows:4,onChange:n=>V(n.target.value),value:j,maxLength:200,showCount:!0})]})]}),e.jsx(Ae,{sports:O,userPermissions:C,courts:q,filterMode:"reservation",onSelectionChange:({sport:n,type:x,subType:P})=>{m(n),S(x),T(P)},isChildren:!0})]})]}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(Ee,{currentMonth:h,selectedDate:w,onDateSelect:M,onPreviousMonth:oe,onNextMonth:s,daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[]})}),e.jsx(Te,{isLoading:v,selectedDate:w,timeRange:u,onTimeClick:o,onNext:p,nextButtonText:"Next: Select coach",startHour:0,endHour:24,interval:30,clubTimes:i!=null&&i.times?JSON.parse(i.times):[],isTimeSlotAvailable:n=>!0})]})})})}),I&&e.jsx(Ye,{coaches:L,onClose:()=>D(!1),selectedDate:w,selectedSport:l,selectedLocation:d,timeRange:ee,sports:O,players:Q,groups:W,club:i,isOpen:I,selectedTimes:u,selectedPlayers:a,selectedType:N,selectedSubType:f,userProfile:U,notes:j,numberOfPlayers:g})]})]})}export{qs as default};
