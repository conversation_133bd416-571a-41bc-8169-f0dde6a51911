import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as R,r as a}from"./vendor-851db8c1.js";import{u as oe}from"./react-hook-form-687afde5.js";import{c as le,a as O}from"./yup-54691517.js";import{o as ne}from"./yup-2824f222.js";import{M as ae,T as he,A as de,G as ce,t as j,aW as ee,a0 as me,H as P,b2 as $,_ as ge,Z as ue,$ as xe,b as V,b3 as be,y as pe}from"./index-6cd5ea29.js";import"./lodash-91d5d207.js";let p=new ae;new he;const fe=le({answer:O().required("Answer is required"),category:O().required("Category is required"),subcategory_id:O().required("Subcategory is required")});function Ae({onClose:r,onSuccess:h,club:i,onDeleteCategory:l,onDeleteSubcategory:S}){const{dispatch:b}=R.useContext(de),{dispatch:d}=R.useContext(ce),[C,I]=a.useState(!1),[_,w]=a.useState([]),[N,m]=a.useState([]),[g,u]=a.useState(!1),[D,U]=a.useState(!1),[k,A]=a.useState(""),[T,L]=a.useState(""),[K,q]=a.useState(!1),[H,W]=a.useState(!1),[Z,Q]=a.useState(!1),[z,J]=a.useState(!1),{register:G,handleSubmit:E,watch:X,setValue:M,formState:{errors:B}}=oe({resolver:ne(fe)}),x=X("category"),y=X("subcategory_id");a.useEffect(()=>{F()},[]),a.useEffect(()=>{x?c(x):m([])},[x]);const F=async()=>{Q(!0);try{p.setTable("faq_category");const s=await p.callRestAPI({filter:i!=null&&i.id?[`club_id,eq,${i.id}`]:["club_id,eq,0"]},"GETALL");w(s.list||[])}catch(s){console.error("Error fetching categories:",s),j(b,s.message)}finally{Q(!1)}},c=async s=>{J(!0);try{p.setTable("faq_subcategory");const n=await p.callRestAPI({filter:[`category_id,eq,${s}`]},"GETALL");m(n.list||[])}catch(n){console.error("Error fetching subcategories:",n),j(b,n.message)}finally{J(!1)}},te=async()=>{if(k.trim()){q(!0);try{p.setTable("faq_category");const s={name:k.trim(),club_id:i!=null&&i.id?i.id:0},n=await p.callRestAPI(s,"POST");if(await ge(p,{user_id:localStorage.getItem("user"),activity_type:ue.faq,action_type:xe.CREATE,data:{newCategoryName:k.trim()},club_id:0,description:"Admin created a FAQ"}),n&&!n.error){const t=n.data;V(d,"Category created successfully"),A(""),u(!1);const o={id:t,name:k.trim(),club_id:i!=null&&i.id?i.id:0};w(v=>[...v,o]),M("category",t.toString()),c(t)}}catch(s){console.error("Error creating category:",s),j(b,s.message)}finally{q(!1)}}},Y=async()=>{if(!(!T.trim()||!x)){W(!0);try{p.setTable("faq_subcategory");const s=await p.callRestAPI({name:T.trim(),category_id:x},"POST");if(s&&!s.error){const n=s.data;V(d,"Subcategory created successfully"),L(""),U(!1);const t={id:n,name:T.trim(),category_id:x};m(o=>[...o,t]),M("subcategory_id",n.toString())}}catch(s){console.error("Error creating subcategory:",s),j(b,s.message)}finally{W(!1)}}},se=async s=>{I(!0);try{p.setTable("faq");const n=await p.callRestAPI({answer:s.answer,subcategory_id:s.subcategory_id,general:i!=null&&i.id?0:1,club_id:i!=null&&i.id?i.id:0},"POST");n&&!n.error&&(V(d,"FAQ added successfully"),h==null||h(),r==null||r())}catch(n){console.error("Error adding FAQ:",n),j(b,n.message)}finally{I(!1)}};return e.jsxs("form",{onSubmit:E(se),className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),g?e.jsxs("button",{type:"button",onClick:()=>u(!g),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(me,{className:"h-4 w-4"}),"Cancel"]}):e.jsxs("button",{type:"button",onClick:()=>u(!g),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(ee,{className:"h-4 w-4"}),"Add New"]})]}),g?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:k,onChange:s=>A(s.target.value),placeholder:"Enter new category name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:te,disabled:K,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:K?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("select",{...G("category"),disabled:Z,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a category"}),Z?e.jsx("option",{value:"",disabled:!0,children:"Loading categories..."}):_.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),_.length>0&&e.jsxs("div",{className:"mt-3 rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("div",{className:"h-1 w-1 rounded-full bg-blue-500"}),e.jsx("p",{className:"text-sm font-semibold text-gray-700",children:"Manage Categories"})]}),e.jsx("div",{className:"max-h-40 space-y-2 overflow-y-auto",children:_.map(s=>e.jsxs("div",{className:"group flex items-center justify-between rounded-lg border border-gray-200 bg-white px-3 py-2.5 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-800",children:s.name})]}),l&&e.jsxs("button",{type:"button",onClick:()=>l(s),className:"flex items-center gap-1.5 rounded-md bg-red-50 px-2.5 py-1.5 text-xs font-medium text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-sm",title:`Delete category: ${s.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete"]})]},s.id))})]})]}),B.category&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:B.category.message})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subcategory"}),e.jsxs("button",{type:"button",onClick:()=>U(!D),disabled:!x,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700 disabled:opacity-50 disabled:hover:text-primaryBlue",children:[e.jsx(ee,{className:"h-4 w-4"}),"Add New"]})]}),D?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:T,onChange:s=>L(s.target.value),placeholder:"Enter new subcategory name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:Y,disabled:H,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:H?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("select",{...G("subcategory_id"),disabled:!x||z,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a subcategory"}),z?e.jsx("option",{value:"",disabled:!0,children:"Loading subcategories..."}):N.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),N.length>0&&x&&e.jsxs("div",{className:"mt-3 rounded-lg border border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("div",{className:"h-1 w-1 rounded-full bg-green-500"}),e.jsx("p",{className:"text-sm font-semibold text-gray-700",children:"Manage Subcategories"})]}),e.jsx("div",{className:"max-h-40 space-y-2 overflow-y-auto",children:N.map(s=>e.jsxs("div",{className:"group flex items-center justify-between rounded-lg border border-gray-200 bg-white px-3 py-2.5 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-800",children:s.name})]}),S&&e.jsxs("button",{type:"button",onClick:()=>S(s),className:"flex items-center gap-1.5 rounded-md bg-red-50 px-2.5 py-1.5 text-xs font-medium text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-sm",title:`Delete subcategory: ${s.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete"]})]},s.id))})]})]}),B.subcategory_id&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:B.subcategory_id.message})]}),y&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Answer"}),e.jsx("textarea",{...G("answer"),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter the answer for this subcategory"}),B.answer&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:B.answer.message})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:r,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:C||!y,className:"flex items-center gap-2 rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:C?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Saving..."]}):"Save"})]})]})}let ie=new ae;const Ee=({faq:r,onEdit:h,onDelete:i,onRefresh:l,onDeleteCategory:S,onDeleteSubcategory:b})=>{var N,m;const[d,C]=R.useState(null),[I,_]=R.useState(!0);async function w(g){try{return ie.setTable("faq_category"),(await ie.callRestAPI({id:g},"GET")).model}catch(u){return console.error("Error fetching FAQ category:",u),null}}return R.useEffect(()=>{var g;w((g=r==null?void 0:r.faq_subcategory)==null?void 0:g.category_id).then(u=>{C(u),_(!1)})},[(N=r==null?void 0:r.faq_subcategory)==null?void 0:N.category_id]),I?e.jsx("div",{children:"Loading..."}):e.jsxs("div",{className:"space-y-6",children:[(h||i)&&e.jsxs("div",{className:"flex justify-end gap-3",children:[h&&e.jsxs("button",{onClick:()=>h(r),className:"flex items-center gap-2 rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:[e.jsx(be,{className:"h-4 w-4"}),"Edit FAQ"]}),i&&e.jsxs("button",{onClick:()=>i(r),className:"flex items-center gap-2 rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700",children:[e.jsx($,{className:"h-4 w-4"}),"Delete FAQ"]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"group relative rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 via-white to-blue-50 p-5 shadow-sm transition-all duration-300 hover:shadow-md",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-blue-100",children:e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"})}),e.jsx("label",{className:"text-sm font-semibold text-gray-700",children:"Category"})]}),d&&S&&e.jsxs("button",{type:"button",onClick:()=>S(d),className:"flex items-center gap-1.5 rounded-lg bg-red-50 px-3 py-1.5 text-xs font-medium text-red-600 shadow-sm transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-md",title:`Delete category: ${d.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete Category"]})]}),e.jsx("div",{className:"rounded-lg bg-white/70 p-3 backdrop-blur-sm",children:e.jsx("p",{className:"text-lg font-semibold text-gray-900",children:(d==null?void 0:d.name)||e.jsx("span",{className:"italic text-gray-400",children:"No category assigned"})})})]}),e.jsxs("div",{className:"group relative rounded-xl border border-gray-200 bg-gradient-to-br from-green-50 via-white to-emerald-50 p-5 shadow-sm transition-all duration-300 hover:shadow-md",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-green-100",children:e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"})}),e.jsx("label",{className:"text-sm font-semibold text-gray-700",children:"Question"})]}),(r==null?void 0:r.faq_subcategory)&&b&&e.jsxs("button",{type:"button",onClick:()=>b(r.faq_subcategory),className:"flex items-center gap-1.5 rounded-lg bg-red-50 px-3 py-1.5 text-xs font-medium text-red-600 shadow-sm transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-md",title:`Delete subcategory: ${r.faq_subcategory.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete Subcategory"]})]}),e.jsx("div",{className:"rounded-lg bg-white/70 p-3 backdrop-blur-sm",children:e.jsx("p",{className:"text-lg font-semibold text-gray-900",children:((m=r==null?void 0:r.faq_subcategory)==null?void 0:m.name)||e.jsx("span",{className:"italic text-gray-400",children:"No subcategory assigned"})})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Answer"}),e.jsxs("div",{className:"mt-1 flex items-start gap-2",children:[e.jsx(pe,{className:"mt-1 h-5 w-5 text-gray-400"}),e.jsx("p",{className:"text-lg text-gray-900",children:(r==null?void 0:r.answer)||"--"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Created At"}),e.jsx("p",{className:"mt-1 text-lg text-gray-900",children:new Date(r==null?void 0:r.create_at).toLocaleDateString()||"--"})]})]})};let f=new ae;const we=le({answer:O().required("Answer is required"),category:O().required("Category is required"),subcategory_id:O().required("Subcategory is required")});function Be({faq:r,onClose:h,onSuccess:i,club:l,onDeleteCategory:S,onDeleteSubcategory:b}){const{dispatch:d}=R.useContext(de),{dispatch:C}=R.useContext(ce),[I,_]=a.useState(!1),[w,N]=a.useState([]),[m,g]=a.useState([]),[u,D]=a.useState(!1),[U,k]=a.useState(!1),[A,T]=a.useState(""),[L,K]=a.useState(""),[q,H]=a.useState(!1),[W,Z]=a.useState(!1),[Q,z]=a.useState(!1),[J,G]=a.useState(!1),[E,X]=a.useState(!1),{register:M,handleSubmit:B,watch:x,setValue:y,formState:{errors:F}}=oe({resolver:ne(we),defaultValues:{answer:"",category:"",subcategory_id:""}}),c=x("category");a.useEffect(()=>{te()},[]),a.useEffect(()=>{c?(E&&y("subcategory_id",""),Y(c)):(g([]),y("subcategory_id",""))},[c,y,E]),a.useEffect(()=>{var t;if(r&&w.length>0&&!E){if(y("answer",r.answer||""),(t=r.faq_subcategory)!=null&&t.category_id){const o=r.faq_subcategory.category_id.toString();y("category",o)}X(!0)}},[r,w,y,E]),a.useEffect(()=>{r&&E&&m.length>0&&c&&!x("subcategory_id")&&r.subcategory_id&&m.find(v=>v.id.toString()===r.subcategory_id.toString())&&y("subcategory_id",r.subcategory_id.toString())},[r,E,m,c,y,x]);const te=async()=>{z(!0);try{f.setTable("faq_category");const t=await f.callRestAPI({filter:l!=null&&l.id?[`club_id,eq,${l.id}`]:["club_id,eq,0"]},"GETALL");N(t.list||[])}catch(t){console.error("Error fetching categories:",t),j(d,t.message)}finally{z(!1)}},Y=async t=>{G(!0);try{f.setTable("faq_subcategory");const o=await f.callRestAPI({filter:[`category_id,eq,${t}`]},"GETALL");return g(o.list||[]),o.list||[]}catch(o){return console.error("Error fetching subcategories:",o),j(d,o.message),[]}finally{G(!1)}},se=async()=>{if(A.trim()){H(!0);try{f.setTable("faq_category");const t={name:A.trim(),club_id:l!=null&&l.id?l.id:0},o=await f.callRestAPI(t,"POST");if(await ge(f,{user_id:localStorage.getItem("user"),activity_type:ue.faq,action_type:xe.CREATE,data:{newCategoryName:A.trim()},club_id:0,description:"Admin created a FAQ category"}),o&&!o.error){const v=o.data;V(C,"Category created successfully"),T(""),D(!1);const re={id:v,name:A.trim(),club_id:l!=null&&l.id?l.id:0};N(ye=>[...ye,re]),y("category",v.toString()),Y(v)}}catch(t){console.error("Error creating category:",t),j(d,t.message)}finally{H(!1)}}},s=async()=>{if(!(!L.trim()||!c)){Z(!0);try{f.setTable("faq_subcategory");const t=await f.callRestAPI({name:L.trim(),category_id:c},"POST");if(t&&!t.error){const o=t.data;V(C,"Subcategory created successfully"),K(""),k(!1);const v={id:o,name:L.trim(),category_id:c};g(re=>[...re,v]),y("subcategory_id",o.toString())}}catch(t){console.error("Error creating subcategory:",t),j(d,t.message)}finally{Z(!1)}}},n=async t=>{_(!0);try{f.setTable("faq");const o=await f.callRestAPI({id:r.id,answer:t.answer,subcategory_id:t.subcategory_id,general:l!=null&&l.id?0:1,club_id:l!=null&&l.id?l.id:0},"PUT");o&&!o.error&&(V(C,"FAQ updated successfully"),i==null||i(),h==null||h())}catch(o){console.error("Error updating FAQ:",o),j(d,o.message)}finally{_(!1)}};return Q?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5 animate-spin text-primaryBlue"}),e.jsx("span",{className:"text-gray-600",children:"Loading FAQ data..."})]})}):e.jsxs("form",{onSubmit:B(n),className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),u?e.jsxs("button",{type:"button",onClick:()=>D(!u),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(me,{className:"h-4 w-4"}),"Cancel"]}):e.jsxs("button",{type:"button",onClick:()=>D(!u),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(ee,{className:"h-4 w-4"}),"Add New"]})]}),u?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:A,onChange:t=>T(t.target.value),placeholder:"Enter new category name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:se,disabled:q,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:q?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("select",{...M("category"),disabled:Q,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a category"}),Q?e.jsx("option",{value:"",disabled:!0,children:"Loading categories..."}):w.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),w.length>0&&e.jsxs("div",{className:"mt-3 rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("div",{className:"h-1 w-1 rounded-full bg-blue-500"}),e.jsx("p",{className:"text-sm font-semibold text-gray-700",children:"Manage Categories"})]}),e.jsx("div",{className:"max-h-40 space-y-2 overflow-y-auto",children:w.map(t=>e.jsxs("div",{className:"group flex items-center justify-between rounded-lg border border-gray-200 bg-white px-3 py-2.5 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-800",children:t.name})]}),S&&e.jsxs("button",{type:"button",onClick:()=>S(t),className:"flex items-center gap-1.5 rounded-md bg-red-50 px-2.5 py-1.5 text-xs font-medium text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-sm",title:`Delete category: ${t.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete"]})]},t.id))})]})]}),F.category&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:F.category.message})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Question"}),e.jsxs("button",{type:"button",onClick:()=>k(!U),disabled:!c,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700 disabled:opacity-50 disabled:hover:text-primaryBlue",children:[e.jsx(ee,{className:"h-4 w-4"}),"Add New"]})]}),U?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:L,onChange:t=>K(t.target.value),placeholder:"Enter new subcategory name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:s,disabled:W,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:W?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("select",{...M("subcategory_id"),disabled:!c||J,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a question"}),J?e.jsx("option",{value:"",disabled:!0,children:"Loading subcategories..."}):m.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),m.length>0&&c&&e.jsxs("div",{className:"mt-3 rounded-lg border border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("div",{className:"h-1 w-1 rounded-full bg-green-500"}),e.jsx("p",{className:"text-sm font-semibold text-gray-700",children:"Manage Subcategories"})]}),e.jsx("div",{className:"max-h-40 space-y-2 overflow-y-auto",children:m.map(t=>e.jsxs("div",{className:"group flex items-center justify-between rounded-lg border border-gray-200 bg-white px-3 py-2.5 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-800",children:t.name})]}),b&&e.jsxs("button",{type:"button",onClick:()=>b(t),className:"flex items-center gap-1.5 rounded-md bg-red-50 px-2.5 py-1.5 text-xs font-medium text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-700 hover:shadow-sm",title:`Delete subcategory: ${t.name}`,children:[e.jsx($,{className:"h-3 w-3"}),"Delete"]})]},t.id))})]})]}),F.subcategory_id&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:F.subcategory_id.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Answer"}),e.jsx("textarea",{...M("answer"),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter the answer for this answer"}),F.answer&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:F.answer.message})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:h,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:I,className:"flex items-center gap-2 rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:I?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 animate-spin"}),"Updating..."]}):"Update FAQ"})]})]})}export{Ae as A,Be as E,Ee as F};
