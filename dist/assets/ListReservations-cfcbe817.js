import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as ve,r as p}from"./vendor-851db8c1.js";import{M as be,T as je,G as ke,A as _e,c as Ne,i as O,Z as M,a6 as Ce,a7 as De,t as Te,a8 as Re,a9 as z,S as Pe,aa as S,ab as Ee}from"./index-6cd5ea29.js";import{c as Ae,a as m}from"./yup-54691517.js";import{u as Le}from"./react-hook-form-687afde5.js";import{o as Fe}from"./yup-2824f222.js";import{P as $e}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Oe from"./Skeleton-1e8bf077.js";import{A as Me,C as ze}from"./CheckinModal-3cebdf42.js";import{L as Ge}from"./LoadingOverlay-87926629.js";import{P as Be,R as Ie,T as Ve,F as qe}from"./ReservationStatus-5ced670f.js";import{D as He}from"./DataTable-8a547681.js";import{H as G}from"./HistoryComponent-9fdeea32.js";let B=new be,Ke=new je;const Ue=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}}],Je=({sports:d,club:r,courts:y})=>{const{dispatch:v,state:Ze}=a.useContext(ke),{dispatch:I}=a.useContext(_e),[V,q]=a.useState([]),[c,b]=a.useState(10),[H,K]=a.useState(0),[Qe,U]=a.useState(0),[u,J]=a.useState(1),[Z,Q]=a.useState(!1),[W,X]=a.useState(!1),[We,Y]=a.useState(!1);a.useState(!1),a.useState([]),a.useState([]),a.useState("eq");const[g,f]=a.useState(!0),[w,Xe]=a.useState(!1),[ee,Ye]=a.useState(!1);a.useState(),ve();const j=a.useRef(null),[se,k]=a.useState(!1),[x,h]=a.useState(null),[te,_]=p.useState(!1),[ae,ie]=p.useState(!1),[ne,oe]=p.useState(!1),[N,le]=p.useState([]),re=Ae({id:m(),email:m(),role:m(),status:m()}),{register:ce,handleSubmit:de,formState:{errors:we}}=Le({resolver:Fe(re)});function ue(){n(u-1,c)}function pe(){n(u+1,c)}const me=async()=>{try{B.setTable("user");const e=await B.callRestAPI({filter:[`club_id,eq,${r==null?void 0:r.id}`,"role,cs,user"]},"GETALL");le(e.list||[])}catch(e){console.error("Error fetching players:",e),showToast(v,"Error fetching players",3e3,"error")}};async function n(e,t,o={},l=[]){f(!(ee||w));try{const i=await Ke.getPaginate("reservation",{page:e,limit:t,filter:[...l,`courtmatchup_reservation.club_id,cs,${r==null?void 0:r.id}`],join:["clubs|club_id","booking|booking_id","user|user_id"],size:c});i&&(f(!1),q(i.list),b(i.limit),K(i.num_pages),J(i.page),U(i.total),Q(i.page>1),X(i.page+1<=i.num_pages))}catch(i){f(!1),console.log("ERROR",i),Te(I,i.message)}}const ge=e=>{e.search?n(1,c,{},[`first_name,cs,${e.search}`,`last_name,cs,${e.search}`]):n(1,c)},fe=async e=>{const t=e.target.value;t===""?await n(1,c):await n(1,c,{},[`sport_id,eq,${parseInt(t)}`])},xe=async e=>{e.target.value===""?await n(u,c):await n(u,c,{},[`courtmatchup_booking.reservation_type,cs,${e.target.value}`])},he=async e=>{e.target.value===""?await n(u,c):await n(u,c,{},[`courtmatchup_booking.status,cs,${e.target.value}`])};a.useEffect(()=>{v({type:"SETPATH",payload:{path:"reservations"}}),r!=null&&r.id&&(n(1,c,{}),me())},[r==null?void 0:r.id]);const C=e=>{j.current&&!j.current.contains(e.target)&&Y(!1)};a.useEffect(()=>(document.addEventListener("mousedown",C),()=>{document.removeEventListener("mousedown",C)}),[]);const Se=e=>{var o,l,i,D,T,R,P,E,A,L,F,$;const t={...e,id:(o=e.booking)==null?void 0:o.id,date:(l=e.booking)==null?void 0:l.date,startTime:(i=e.booking)==null?void 0:i.start_time,endTime:(D=e.booking)==null?void 0:D.end_time,sport_id:(T=e.booking)==null?void 0:T.sport_id,type:(R=e.booking)==null?void 0:R.type,sub_type:(P=e.booking)==null?void 0:P.subtype,reservation_type:(E=e.booking)==null?void 0:E.reservation_type,price:(A=e.booking)==null?void 0:A.price,status:(L=e.booking)==null?void 0:L.status,player_ids:(F=e.booking)==null?void 0:F.player_ids,coach_ids:($=e.booking)==null?void 0:$.coach_ids};h(t),k(!0)},ye={create_at:e=>s.jsx("span",{className:"text-gray-600",children:new Date(e.create_at).toLocaleDateString("en-GB",{day:"2-digit",month:"long",year:"numeric"})}),type:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=O.find(o=>{var l;return o.value==((l=e==null?void 0:e.booking)==null?void 0:l.reservation_type)}))==null?void 0:t.label)||"--"})},sport:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=d.find(o=>{var l;return o.id===((l=e==null?void 0:e.booking)==null?void 0:l.sport_id)}))==null?void 0:t.name)||"--"})},date:e=>{var t,o,l;return s.jsxs(s.Fragment,{children:[Re((t=e==null?void 0:e.booking)==null?void 0:t.date)," "," | "," ",z((o=e==null?void 0:e.booking)==null?void 0:o.start_time)," "," - "," ",z((l=e==null?void 0:e.booking)==null?void 0:l.end_time)]})},players:e=>{var t,o;return s.jsx(s.Fragment,{children:(t=e==null?void 0:e.booking)!=null&&t.player_ids?`${JSON.parse((o=e==null?void 0:e.booking)==null?void 0:o.player_ids).length} players`:"0 players"})},bill:e=>{var t;return s.jsx(s.Fragment,{children:Pe((t=e==null?void 0:e.booking)==null?void 0:t.price)})},user:e=>{var t,o,l,i;return s.jsx(s.Fragment,{children:!((t=e==null?void 0:e.user)!=null&&t.first_name)||!((o=e==null?void 0:e.user)!=null&&o.last_name)?"--":`${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(i=e==null?void 0:e.user)==null?void 0:i.last_name}`})},status:e=>s.jsxs(s.Fragment,{children:[e.booking.status==S.SUCCESS&&s.jsx(Be,{}),e.booking.status==S.PENDING&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Ie,{}),s.jsx(Ve,{timeLeft:Ee(e==null?void 0:e.reservation_updated_at)})]}),e.booking.status==S.FAIL&&s.jsx(qe,{})]})};return s.jsxs("div",{className:"h-screen px-4 sm:px-6 lg:px-8",children:[g&&s.jsx(Ge,{}),s.jsxs("div",{className:"flex flex-col gap-4 py-3 sm:gap-6",children:[s.jsx("div",{className:"w-full max-w-xl",children:s.jsxs("form",{className:"relative flex items-center",onSubmit:de(ge),children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(Ne,{className:"h-4 w-4 text-gray-500 sm:h-5 sm:w-5"})}),s.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:py-2.5 sm:pl-11",placeholder:"Search reservations...",...ce("search")})]})}),s.jsxs("div",{className:"flex flex-col gap-4",children:[s.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[s.jsx("input",{type:"date",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),s.jsx("input",{type:"time",defaultValue:"00:00",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),s.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",defaultValue:"Sport: All",onChange:fe,children:[s.jsx("option",{value:"",children:"Sport: All"}),d==null?void 0:d.map(e=>s.jsx("option",{value:e.id,children:e.name},e.id))]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",defaultValue:"All",onChange:he,children:[s.jsx("option",{value:"",children:"Status: All"}),s.jsx("option",{value:"0",children:"Reserved"}),s.jsx("option",{value:"1",children:"Paid"}),s.jsx("option",{value:"2",children:"Failed"})]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",defaultValue:"All",onChange:xe,children:[s.jsx("option",{value:"",children:"Reservation Type: All"}),O.map(e=>s.jsx("option",{value:e.value,children:e.label},e.value))]})]}),s.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[s.jsxs("button",{onClick:()=>_(!0),className:"inline-flex items-center justify-center gap-2 rounded-lg bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4",children:[s.jsx("span",{className:"text-sm",children:"+"}),s.jsx("span",{className:"hidden sm:inline",children:"Add new"}),s.jsx("span",{className:"sm:hidden",children:"Add"})]}),s.jsx("div",{className:"hidden sm:block",children:s.jsx(G,{activityType:M.court_reservation,emptyMessage:"No reservations found",title:"Reservations"})})]}),s.jsx("div",{className:"sm:hidden",children:s.jsx(G,{activityType:M.court_reservation,emptyMessage:"No reservations found",title:"Reservations"})})]})]}),g?s.jsx(Oe,{}):s.jsx(He,{columns:Ue,data:V,loading:g,renderCustomCell:ye,emptyMessage:"No reservations available",loadingMessage:"Loading reservations...",onClick:e=>Se(e)}),s.jsx($e,{currentPage:u,pageCount:H,pageSize:c,canPreviousPage:Z,canNextPage:W,updatePageSize:e=>{b(e),n(1,e)},previousPage:ue,nextPage:pe,gotoPage:e=>n(e,c)}),s.jsx(Ce,{isOpen:se,onClose:()=>k(!1),event:x,users:N,sports:d,club:r,fetchData:n,courts:y}),s.jsx(Me,{isOpen:te,club:r,onClose:()=>_(!1),sports:d,players:N}),ae&&s.jsx(ze,{courts:y,onClose:()=>ie(!1),reservation:x,getData:n,sports:d,setReservation:h}),ne&&s.jsx(De,{reservation:x,onClose:()=>oe(!1),getData:n,setReservation:h})]})},gs=Je;export{gs as L};
