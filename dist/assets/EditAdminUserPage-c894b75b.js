import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as x,f as G,j as q,r as f}from"./vendor-851db8c1.js";import{u as B}from"./react-hook-form-687afde5.js";import{o as D}from"./yup-2824f222.js";import{c as M,a as h}from"./yup-54691517.js";import{w as H,M as I,A as K,G as V,t as P,b}from"./index-6cd5ea29.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let l=new I;const Y=({activeId:i,setSidebar:g})=>{var N,k,S,A;const C=M({email:h().email().required(),password:h(),role:h()}).required(),{dispatch:y}=x.useContext(K),{dispatch:n}=x.useContext(V),T=G();q();const[R,U]=f.useState(""),[z,$]=f.useState(0),[w,j]=f.useState(!1),{register:d,handleSubmit:v,setError:c,setValue:u,formState:{errors:p}}=B({resolver:D(C)}),F=["club","staff","user","coach","admin"],O=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],E=async e=>{j(!0);try{if(R!==e.email){const t=await l.updateEmailByAdmin(e.email,i);if(!t.error)b(n,"Email Updated",1e3);else if(t.validation){const o=Object.keys(t.validation);for(let a=0;a<o.length;a++){const m=o[a];c(m,{type:"manual",message:t.validation[m]})}}}if(e.password.length>0){const t=await l.updatePasswordByAdmin(e.password,i);if(!t.error)b(n,"Password Updated",2e3);else if(t.validation){const o=Object.keys(t.validation);for(let a=0;a<o.length;a++){const m=o[a];c(m,{type:"manual",message:t.validation[m]})}}}l.setTable("user");const r=await l.callRestAPI({activeId:i,email:e.email,role:e.role,status:e.status},"PUT");if(!r.error)b(n,"Added",4e3),T("/admin/users");else if(r.validation){const t=Object.keys(r.validation);for(let o=0;o<t.length;o++){const a=t[o];c(a,{type:"manual",message:r.validation[a]})}}}catch(r){console.log("Error",r),c("email",{type:"manual",message:r.message}),P(y,r.message)}j(!1)};return x.useEffect(()=>{n({type:"SETPATH",payload:{path:"users"}}),async function(){try{l.setTable("user");const e=await l.callRestAPI({id:i},"GET");e.error||(u("email",e.model.email),u("role",e.model.role),u("status",e.model.status),U(e.model.email),$(e.model.id))}catch(e){console.log("Error",e),P(y,e.message)}}()},[i]),s.jsxs("div",{className:"mx-auto rounded",children:[s.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>g(!1),children:"Cancel"}),s.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await v(E)(),g(!1)},disabled:w,children:w?"Saving...":"Save"})]})]}),s.jsxs("form",{className:" w-full p-4 text-left",onSubmit:v(E),children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),s.jsx("input",{type:"email",...d("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=p.email)!=null&&N.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(k=p.email)==null?void 0:k.message})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("role"),children:F.map(e=>s.jsx("option",{name:"role",value:e,children:e},e))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("status"),children:O.map(e=>s.jsx("option",{name:"status",value:e.key,children:e.value},e.key))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...d("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(S=p.password)!=null&&S.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(A=p.password)==null?void 0:A.message})]})]})]})},Ce=H(Y,"user","You don't have permission to edit users");export{Ce as default};
