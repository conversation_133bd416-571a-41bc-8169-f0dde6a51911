import{j as E}from"./@nivo/heatmap-ba1ecfff.js";import{b as p,r as j}from"./vendor-851db8c1.js";import{M as C,G as R,u as $,b as n}from"./index-6cd5ea29.js";import"./lodash-91d5d207.js";import{I as A}from"./InvoiceList-9871b51a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./index.esm-3a36c7d6.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./StripeConnectionStatus-2118dd38.js";import"./HistoryComponent-9fdeea32.js";let F=new C;const Pt=()=>{const{dispatch:w}=p.useContext(R),[G,s]=p.useState([]),[N,m]=p.useState(!1),{club:o,sports:S,fetchClubData:T}=$();p.useEffect(()=>{w({type:"SETPATH",payload:{path:"invoicing"}})},[]);async function f(P=1,D=10,L={}){m(!0);try{const{invoice_id:e="",firstName:l="",lastName:u="",sportId:d="",receiptId:b="",bookingType:h="",from:v="",until:g="",timeFrom:y="",timeUntil:_="",month:k="",year:I="",sort:x="desc",tabType:c="users"}=L,t=new URLSearchParams;t.append("page",P),t.append("limit",D),e&&t.append("receipt_id",e),l&&t.append("first_name",l),u&&t.append("last_name",u),d&&t.append("sport_id",d),b&&t.append("receipt_id",b),h&&t.append("booking_type",h),v&&t.append("from",v),g&&t.append("until",g),y&&t.append("time_from",y),_&&t.append("time_until",_),k&&t.append("month",k),I&&t.append("year",I),x&&t.append("sort",x);let r;switch(c){case"users":r="/v3/api/custom/courtmatchup/club/billing/invoices";break;case"coaches":r=`/v3/api/custom/courtmatchup/club/billing/coach-invoices?club_id=${o==null?void 0:o.id}`;break;case"staff":r="/v3/api/custom/courtmatchup/club/billing/staff-invoices";break;default:r="/v3/api/custom/courtmatchup/club/billing/invoices"}try{const i=await F.callRawAPI(`${r}?${t.toString()}`,{},"GET");if(i.error){n(i.error,"error"),s([]);return}let a=[];switch(c){case"users":a=i.invoices||[];break;case"coaches":a=i.coach_invoices||[];break;case"staff":a=i.staff_invoices||[];break;default:a=i.invoices||[]}s(a)}catch(i){console.error(`API Error for ${c} tab:`,i),s([]),i.status!==404&&n("Failed to fetch invoices","error")}}catch(e){console.error(e),n(e.message||"Failed to fetch invoices","error"),s([])}finally{m(!1)}}return j.useEffect(()=>{f(1,10,{tabType:"users"})},[]),E.jsx("div",{className:"h-screen px-8",children:E.jsx(A,{clubProfile:o,getData:T,sports:S,invoices:[],fetchInvoices:f,club:o})})};export{Pt as default};
