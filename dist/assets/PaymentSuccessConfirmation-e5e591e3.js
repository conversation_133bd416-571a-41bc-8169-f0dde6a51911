import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{f as w,j as _,r as d,k as v}from"./vendor-851db8c1.js";import{g as C}from"./index.esm-3f8dc7b8.js";import{u as k}from"./@tanstack/react-query-20158223.js";import{G as E,A as P,u as T,J as S,e as R,ag as j,S as t,M as A}from"./index-6cd5ea29.js";import{f as G}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-icons-51bc3cff.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let a=new A;function ue(){var r,o,x,p,h,u;const f=w(),{reservation_id:l}=_();d.useContext(E),d.useContext(P);const[b]=v(),n=b.get("type"),{club:c,sports:M,loading:N}=T(),{data:e,isPending:g}=k({queryKey:["reservation",l],queryFn:async()=>{const i=await a.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/${n}/${l}`,{},"GET"),m=await a.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices/${l}`,{},"GET");a.setTable("user");const y=await a.callRestAPI({id:i.reservation.booking_user_id},"GET");return console.log(i),{...i.reservation,user:y.model,invoice:m.invoices[0]}}});return d.useEffect(()=>{S({path:`/user/reserve-court?type=${n}`,clubName:c==null?void 0:c.name,favicon:c==null?void 0:c.club_logo,description:"Reservation successful"})},[c==null?void 0:c.club_logo,n]),console.log(e),s.jsxs("div",{className:"min-h-screen ",children:[s.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:s.jsx("p",{children:"You're all set!"})}),g||N?s.jsx(R,{}):s.jsx("div",{className:"h-full py-8",children:s.jsxs("div",{className:"mx-auto max-w-md space-y-6 rounded-xl bg-white p-6 shadow-sm",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(C,{className:"h-5 w-5 text-green-500"}),s.jsx("p",{className:"!mb-0 !mt-0 text-lg font-medium",children:"Confirmation"})]}),s.jsxs("div",{className:"space-y-5",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"DETAILS"}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{children:[s.jsx("span",{children:"Reserving"}),s.jsxs("div",{className:"text-right",children:[s.jsx("div",{children:e!=null&&e.booking_date?G(new Date(e.booking_date+"T00:00:00"),"MMM d, yyyy"):""}),s.jsxs("div",{children:[j(e==null?void 0:e.start_time)," -"," ",j(e==null?void 0:e.end_time)]})]})]}),(e==null?void 0:e.coach_id)&&s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Coach"}),s.jsx("span",{children:(r=e==null?void 0:e.coach)==null?void 0:r.first_name})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Players"}),s.jsx("div",{className:"text-right",children:(o=e==null?void 0:e.players)==null?void 0:o.map((i,m)=>s.jsxs("div",{className:"capitalize",children:[i==null?void 0:i.first_name," ",i==null?void 0:i.last_name]},m))})]})]})]}),s.jsxs("div",{className:"border-t pt-3",children:[s.jsx("h2",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"FEES"}),(e==null?void 0:e.booking_type)==="Court"&&s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Club fee"}),s.jsx("div",{className:"text-right",children:s.jsx("div",{children:t(Number(e==null?void 0:e.club_fee))})})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Service fee"}),s.jsx("span",{children:t(Number(e==null?void 0:e.service_fee))})]}),s.jsxs("div",{className:"flex justify-between border-b border-t pb-3 pt-3",children:[s.jsx("span",{className:"font-medium",children:"Total"}),s.jsx("span",{className:"font-medium",children:t(Number(e==null?void 0:e.club_fee)+Number(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_type)==="Lesson"&&s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Coach fee"}),s.jsx("div",{className:"text-right",children:s.jsx("div",{children:t(Number(e==null?void 0:e.coach_fee))})})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Service fee"}),s.jsx("span",{children:t(Number(e==null?void 0:e.service_fee))})]}),s.jsxs("div",{className:"flex justify-between border-b border-t pb-3 pt-3",children:[s.jsx("span",{className:"font-medium",children:"Total"}),s.jsx("span",{className:"font-medium",children:t(Number(e==null?void 0:e.coach_fee)+Number(e==null?void 0:e.service_fee))})]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Receipt confirmation"}),s.jsxs("span",{children:["#",(x=e==null?void 0:e.invoice)==null?void 0:x.receipt_id]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Payment method"}),s.jsx("span",{children:(p=e==null?void 0:e.invoice)!=null&&p.last_4?`Card • ${(h=e==null?void 0:e.invoice)==null?void 0:h.last_4}`:"N/A"})]})]}),s.jsxs("p",{className:"text-center text-sm text-gray-500",children:["Confirmation has been sent to ",(u=e==null?void 0:e.user)==null?void 0:u.email]}),s.jsxs("div",{className:"flex flex-col gap-3",children:[s.jsx("button",{onClick:()=>f("/user/dashboard"),className:"w-full rounded-lg bg-[#1E2841] px-4 py-3 text-white transition hover:bg-[#1E2841]/90",children:"Back to Home"}),s.jsx("button",{onClick:()=>window.print(),className:"w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 transition hover:bg-gray-50",children:"Print receipt"})]})]})]})})]})}export{ue as default};
