import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as te,k as Ce,f as Se}from"./vendor-851db8c1.js";import{M as pe,G as me,b as E,e as de,T as Te,A as ke,d as we,t as Ne,u as Ie,f as Ae,h as Ee,i as Pe,R as Le,D as $e,F as Re,j as De,k as Fe,l as Me}from"./index-6cd5ea29.js";import{B as Oe}from"./BackButton-11ba52b2.js";import{S as Be}from"./StripeConnectionStatus-2118dd38.js";import{u as Ue}from"./react-hook-form-687afde5.js";import{o as ze}from"./yup-2824f222.js";import{c as Ge,a as <PERSON>}from"./yup-54691517.js";import"./index-02625b16.js";import{I as qe}from"./ImageCropModal-54287cc3.js";import{L as Ze}from"./index.esm-3a36c7d6.js";import{b as ie}from"./index.esm-9c6194ba.js";import{M as ce}from"./react-tooltip-7a26650a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-image-crop-1f5038af.js";const Ve=new pe;function We(){const[h,P]=r.useState([{account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"}]);r.useState(!1),r.useState(null),r.useState({}),r.useState(!1),r.useState({account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"});const{dispatch:m}=te.useContext(me),[$,_]=r.useState(!0),R=te.useRef(null),x=c=>{console.log("Stripe connection status changed:",c)};r.useEffect(()=>{C()},[]);const C=async()=>{_(!0);try{const c=await Ve.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),S=JSON.parse(c.account_details||"[]");S.length>0&&Array.isArray(S)&&P(S)}catch(c){E(m,c.message,3e3,"error")}finally{_(!1)}};return e.jsxs("div",{className:"mx-auto flex w-full flex-col bg-white px-4 pb-7",children:[$&&e.jsx(de,{}),e.jsxs("div",{className:"mx-auto flex w-full max-w-2xl flex-col justify-center",children:[e.jsx(Be,{ref:R,onConnectionStatusChange:x,successMessage:"You can now pay staffs and coaches from your club.",noConnectionMessage:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),e.jsx("div",{className:"flex w-full flex-col self-center",children:e.jsx("p",{className:"mt-6 text-xs leading-4 text-neutral-400",children:"Note: Multiple accounts can be set up if you want to change accounts later on a particular month or year."})})]})]})}let oe=new pe,Ye=new Te;const Je=()=>{const h=Ge({email:He().email().required()}).required(),{dispatch:P}=te.useContext(ke),[m,$]=r.useState("");te.useState({});const[_,R]=r.useState("");r.useState(!1);const[x,C]=r.useState(!1),[c,S]=r.useState({}),[T,D]=r.useState(!0),[le,v]=r.useState(null),[F,M]=r.useState(""),{dispatch:k}=te.useContext(me),[ne,G]=r.useState(!1),[f,Y]=r.useState(null),[j,H]=r.useState(!1),[J,se]=r.useState(null),{register:K,handleSubmit:U,setError:s,setValue:d,formState:{errors:u}}=Ue({resolver:ze(h)}),I=localStorage.getItem("user");async function L(){var i;D(!0);try{const o=await Ye.getList("profile",{filter:[`user_id,eq,${I}`],join:["user|user_id"]}),l=(i=o==null?void 0:o.list)==null?void 0:i[0];if(l){const n=l.user||{},N=l.id,B={...l,...n,profile_id:N,user_id:n.id};S(B),d("email",n==null?void 0:n.email),d("first_name",n==null?void 0:n.first_name),d("last_name",n==null?void 0:n.last_name),d("phone",n==null?void 0:n.phone),d("bio",n==null?void 0:n.bio),$(n==null?void 0:n.email),R(n==null?void 0:n.photo),d("gender",l==null?void 0:l.gender),d("address",l==null?void 0:l.address),d("city",l==null?void 0:l.city),d("state",l==null?void 0:l.state),d("zip_code",l==null?void 0:l.zip_code),P({type:"UPDATE_PROFILE",payload:B}),D(!1)}}catch(o){Ne(P,o.response.data.message?o.response.data.message:o.message)}}const z=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],ae=["gender","address","city","state","zip_code","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:c==null?void 0:c.user_id,profile_id:c==null?void 0:c.profile_id,defaultValues:c});const q=async(i,o)=>{try{C(!0);const l={[i]:o},n=z.includes(i),N=ae.includes(i);if(n){oe.setTable("user");const B=await oe.callRestAPI({id:c==null?void 0:c.user_id,...l},"PUT");B.error?Q(B):(E(k,"Profile Updated",4e3),v(null),M(""),L())}else if(N){oe.setTable("profile");const B=await oe.callRestAPI({id:c==null?void 0:c.profile_id,...l},"PUT");B.error?Q(B):(E(k,"Profile Updated",4e3),v(null),M(""),L())}else{E(k,"Unknown field type: "+i,4e3,"error"),C(!1);return}C(!1)}catch(l){C(!1),s(i,{type:"manual",message:l!=null&&l.message&&l==null?void 0:l.message}),Ne(P,l!=null&&l.message&&l==null?void 0:l.message)}},Q=i=>{if(i.validation){const o=Object.keys(i.validation);for(let l=0;l<o.length;l++){const n=o[l];s(n,{type:"manual",message:i.validation[n]})}}},Z=i=>{try{if(i.size>2*1024*1024){E(k,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}se(i.type);const o=new FileReader;o.onload=()=>{Y(o.result),H(!0)},o.readAsDataURL(i)}catch(o){E(k,o==null?void 0:o.message,3e3,"error"),console.log(o)}},V=async i=>{try{G(!0);const o=J==="image/png",l=new File([i],`cropped_profile.${o?"png":"jpg"}`,{type:o?"image/png":"image/jpeg"});let n=new FormData;n.append("file",l);let N=await oe.uploadImage(n);q("photo",N==null?void 0:N.url)}catch(o){E(k,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{G(!1)}},O=()=>{q("photo",null),S({...c,photo:null})};return te.useEffect(()=>{L()},[]),e.jsxs("div",{className:"",children:[T||ne&&e.jsx(de,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(qe,{isOpen:j,onClose:()=>H(!1),image:f,onCropComplete:V}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:_||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:O,disabled:!_,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:i=>Z(i.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"}].map(i=>e.jsx("div",{children:le===i.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:i.label}),e.jsx("button",{onClick:()=>v(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),i.type==="select"?e.jsxs("select",{value:F,onChange:o=>M(o.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",i.label.toLowerCase()]}),i.options.map(o=>e.jsx("option",{value:o,children:o.charAt(0).toUpperCase()+o.slice(1)},o))]}):i.type==="textarea"?e.jsx("textarea",{value:F,onChange:o=>M(o.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:F,onChange:o=>M(o.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note}),e.jsx("div",{className:"mt-2",children:e.jsx(we,{loading:x,onClick:()=>q(i.key,F),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:i.label}),e.jsx("button",{onClick:()=>{v(i.key),M((c==null?void 0:c[i.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(c==null?void 0:c[i.key])||"--"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note})]})},i.key))})]})]})})]})};let Ke=new pe;const Qe=()=>{const[h,P]=r.useState({}),[m,$]=r.useState(""),[_,R]=r.useState(""),[x,C]=r.useState(""),[c,S]=r.useState("date"),[T,D]=r.useState("desc"),[le,v]=r.useState([]),[F,M]=r.useState(!1),[k,ne]=r.useState(null),[G,f]=r.useState("all");Ie();const Y=s=>{P(d=>({...d,[s]:!d[s]}))},j=async()=>{M(!0);try{const s=await Ke.callRawAPI("/v3/api/custom/courtmatchup/coach/bookings/billing/coach-invoices",{},"GET");ne(s),v(s.invoices||[])}catch(s){console.log(s)}finally{M(!1)}},J=[...le.filter(s=>{var L,z,ae,q,Q;const d=((L=s.user_first_name)==null?void 0:L.toLowerCase().includes(m.toLowerCase()))||((z=s.user_last_name)==null?void 0:z.toLowerCase().includes(m.toLowerCase()))||((ae=s.receipt_id)==null?void 0:ae.toLowerCase().includes(m.toLowerCase()))||((q=s.status)==null?void 0:q.toLowerCase().includes(m.toLowerCase())),u=G==="all"||((Q=s.invoice_type)==null?void 0:Q.toLowerCase())===G.toLowerCase(),I=(()=>{if(!_&&!x)return!0;if(!s.date)return!1;const Z=new Date(s.date),V=_?new Date(_):null,O=x?new Date(x):null;return V&&O?Z>=V&&Z<=O:V?Z>=V:O?Z<=O:!0})();return d&&u&&I})].sort((s,d)=>{let u=0;if(c==="date"){const I=new Date(s.date||s.create_at),L=new Date(d.date||d.create_at);u=I.getTime()-L.getTime()}else if(c==="amount")u=(s.amount||0)-(d.amount||0);else if(c==="status")u=(s.status||"").localeCompare(d.status||"");else if(c==="customer"){const I=`${s.user_first_name||""} ${s.user_last_name||""}`.trim(),L=`${d.user_first_name||""} ${d.user_last_name||""}`.trim();u=I.localeCompare(L)}return T==="desc"?-u:u}),se=()=>{c==="date"?(S("amount"),D("desc")):c==="amount"?(S("status"),D("asc")):c==="status"?(S("customer"),D("asc")):(S("date"),D(T==="desc"?"asc":"desc"))},K=(s,d="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:d.toUpperCase()}).format(s),U=s=>s?new Date(s).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A";return r.useEffect(()=>{j()},[]),F?e.jsx("div",{className:"flex w-full items-center justify-center py-8",children:e.jsx("div",{className:"text-lg",children:"Loading invoices..."})}):e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"My invoices"}),k&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Total: ",k.total_invoices||0," invoices • Total Earnings: ",K(k.total_earnings||0)]})]})}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Ae,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:m,onChange:s=>$(s.target.value),placeholder:"Search invoices...",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:_,onChange:s=>R(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:x,onChange:s=>C(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(_||x)&&e.jsx("button",{onClick:()=>{R(""),C("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:se,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:["By ",c," ",T==="desc"?"↓":"↑",e.jsx(Ze,{className:"transform"})]})]}),e.jsx("div",{className:"space-y-4",children:J.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No invoices found."}):J.map(s=>{var d;return e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>Y(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ee,{className:`transform transition-transform ${h[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.invoice_type||s.type}),s.status&&e.jsx("span",{className:`rounded-full px-2 py-1 text-xs ${s.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:U(s.date)}),e.jsx("span",{className:"font-medium",children:K(s.amount,s.currency)})]})]}),h[s.id]&&e.jsxs("div",{className:"mt-4 space-y-3 border-t border-gray-200 p-4",children:[s.receipt_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice Number"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),s.user_first_name&&s.user_last_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer"}),e.jsxs("span",{children:[s.user_first_name," ",s.user_last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created on"}),e.jsx("span",{children:U(s.create_at)})]}),s.total_amount&&s.total_amount!==s.amount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total amount"}),e.jsx("span",{children:K(s.total_amount,s.currency)})]}),s.valid_until&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Period"}),e.jsxs("span",{children:[U(s.create_at)," -"," ",U(s.valid_until)]})]}),s.club_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club"}),e.jsx("span",{children:s.club_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),s.reservation_type&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Reservation type"}),e.jsx("span",{className:"capitalize",children:(d=Pe.find(u=>u.value===s.reservation_type))==null?void 0:d.label})]}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50",onClick:()=>{const u=`
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                          <h2>Invoice Receipt</h2>
                          <p><strong>Receipt ID:</strong> ${s.receipt_id||"N/A"}</p>
                          <p><strong>Amount:</strong> ${K(s.amount,s.currency)}</p>
                          <p><strong>Date:</strong> ${U(s.date)}</p>
                          <p><strong>Status:</strong> ${s.status}</p>
                          <p><strong>Customer:</strong> ${s.user_first_name} ${s.user_last_name}</p>
                          <p><strong>Payment Method:</strong> ${s.payment_method}</p>
                          ${s.club_name?`<p><strong>Club:</strong> ${s.club_name}</p>`:""}
                        </div>
                      `,I=window.open("","_blank");I.document.write(u),I.document.close(),I.print()},children:"Print receipt"})]})]},s.id)})})]})},A=new pe,Xe=()=>{var B,xe,ye,he,fe,ge,be,je,_e,ve;const[h,P]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[m,$]=r.useState({}),[_,R]=r.useState(!1),[x,C]=r.useState(!1),[c,S]=r.useState(!0),{dispatch:T}=r.useContext(me),[D,le]=r.useState([]),[v,F]=r.useState(null),[M,k]=r.useState(!1),[ne,G]=r.useState(null),[f,Y]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[j,H]=r.useState({}),[J,se]=r.useState(!1),[K,U]=r.useState(!1),[s,d]=r.useState(null),u=(D==null?void 0:D.filter(t=>t.status===1))||[];r.useEffect(()=>{(async()=>{S(!0);try{const a=await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");F(a);const y=await A.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${a.club_id}`,{},"GET");le(y.sports)}catch(a){console.error("Error fetching data:",a),E(T,a.message,3e3,"error")}finally{S(!1)}})()},[T]);const I=(t,a=null)=>{var W;const y=t.sport_id?parseInt(t.sport_id):null,g=t.type||"",b=t.sub_type||"";if((W=v==null?void 0:v.sports)==null?void 0:W.find(p=>(y?parseInt(p.sport_id)===y:!p.sport_id||p.sport_id===0)&&(g?(p.type||"")===g:!p.type||p.type==="")&&(b?(p.sub_type||"")===b:!p.sub_type||p.sub_type==="")&&(!a||p.id!==a))){const p=u.find(ue=>ue.id===y);return{hasConflict:!0,message:`A price for ${y?p==null?void 0:p.name:"all sports"} (${g||"all types"}, ${b||"all sub-types"}) combination already exists. Please edit the existing price instead.`}}return{hasConflict:!1}},L=t=>{const a={};t.sport_id||(a.sport_id="Sport is required");const y=I(t);return y.hasConflict&&(a.duplicate=y.message),t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),a},z=t=>{const{name:a,value:y}=t.target;P(g=>{const b={...g,[a]:y};return a==="sport_id"&&(b.type="",b.sub_type=""),a==="type"&&(b.sub_type=""),b}),$(g=>({...g,[a]:"",...["sport_id","type","sub_type"].includes(a)&&{duplicate:""}}))},ae=async()=>{var b,re,W;const t={...h},a=u.find(p=>p.id===parseInt(h.sport_id));if(!(((b=a==null?void 0:a.sport_types)==null?void 0:b.length)>0&&a.sport_types.some(p=>p.type&&p.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const p=(re=a==null?void 0:a.sport_types)==null?void 0:re.find(X=>X.type===t.type);((W=p==null?void 0:p.subtype)==null?void 0:W.length)>0&&p.subtype.some(X=>X&&X.trim()!=="")||(t.sub_type="")}const g=L(t);if(Object.keys(g).length>0){$(g);return}C(!0);try{A.setTable("coach_sports"),await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[t]},"POST"),P({sport_id:"",type:"",sub_type:"",price:""});const p=await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");F(p),E(T,"Sport added successfully",3e3,"success")}catch(p){console.log(p),E(T,p.message,3e3,"error")}finally{C(!1)}},q=t=>{d(t),U(!0)},Q=async()=>{if(s){R(!0);try{A.setTable("coach_sports"),await A.callRestAPI({id:s.id},"DELETE");const t=await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");F(t),E(T,"Sport deleted successfully",3e3,"success"),U(!1),d(null)}catch(t){console.log(t),E(T,t.message,3e3,"error")}finally{R(!1)}}},Z=()=>{U(!1),d(null)},V=t=>{G(t),Y({id:t.id,sport_id:t.sport_id,type:t.type||"",sub_type:t.sub_type||"",price:t.price||""}),H({}),k(!0)},O=t=>{const{name:a,value:y}=t.target;Y(g=>{const b={...g,[a]:y};return a==="sport_id"&&(b.type="",b.sub_type=""),a==="type"&&(b.sub_type=""),b}),H(g=>({...g,[a]:"",...["sport_id","type","sub_type"].includes(a)&&{duplicate:""}}))},i=t=>{const a={};t.sport_id||(a.sport_id="Sport is required");const y=I(t,t.id);return y.hasConflict&&(a.duplicate=y.message),t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),a},o=async()=>{var b,re,W,p;const t={...f},a=u.find(w=>w.id===parseInt(f.sport_id));if(!(((b=a==null?void 0:a.sport_types)==null?void 0:b.length)>0&&a.sport_types.some(w=>w.type&&w.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const w=(re=a==null?void 0:a.sport_types)==null?void 0:re.find(ee=>ee.type===t.type);((W=w==null?void 0:w.subtype)==null?void 0:W.length)>0&&w.subtype.some(ee=>ee&&ee.trim()!=="")||(t.sub_type="")}const g=i(t);if(Object.keys(g).length>0){H(g);return}se(!0);try{const w=(p=v==null?void 0:v.sports)==null?void 0:p.find(ue=>ue.id===f.id);w&&parseInt(f.sport_id)!==parseInt(w.sport_id)?(A.setTable("coach_sports"),await A.callRestAPI({id:f.id},"DELETE"),await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[{sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price}]},"POST")):(A.setTable("coach_sports"),await A.callRestAPI({id:f.id,sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price},"PUT"));const ee=await A.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");F(ee),k(!1),E(T,"Sport updated successfully",3e3,"success")}catch(w){console.log(w),E(T,w.message,3e3,"error")}finally{se(!1)}},l=()=>{k(!1),G(null),Y({sport_id:"",type:"",sub_type:"",price:""}),H({})},n=u.find(t=>t.id===parseInt(h.sport_id)),N=u.find(t=>t.id===parseInt(f.sport_id));return c?e.jsx(de,{}):e.jsxs("div",{className:"w-full",children:[_&&e.jsx(de,{}),e.jsx("h1",{className:"mb-6 text-xl font-semibold text-gray-900",children:"Sports you offer"}),e.jsxs("div",{className:"space-y-6",children:[((B=v==null?void 0:v.sports)==null?void 0:B.length)>0?e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:e.jsx("ul",{className:"divide-y divide-gray-200",children:v.sports.map((t,a)=>{var y;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(y=u.find(g=>g.id===parseInt(t.sport_id)))==null?void 0:y.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:t.type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:t.sub_type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",t.price]})]})]}),e.jsxs("div",{className:"ml-4 flex gap-2",children:[e.jsx("button",{onClick:()=>V(t),className:"text-blue-500 hover:text-blue-700",title:"Edit sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.1667 2.5C14.3856 2.28113 14.6454 2.10752 14.9312 1.98906C15.2169 1.87061 15.5238 1.80957 15.8333 1.80957C16.1429 1.80957 16.4498 1.87061 16.7355 1.98906C17.0213 2.10752 17.2811 2.28113 17.5 2.5C17.7189 2.71887 17.8925 2.97869 18.0109 3.26445C18.1294 3.55021 18.1904 3.85714 18.1904 4.16667C18.1904 4.47619 18.1294 4.78312 18.0109 5.06888C17.8925 5.35464 17.7189 5.61446 17.5 5.83333L6.25 17.0833L1.66667 18.3333L2.91667 13.75L14.1667 2.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{onClick:()=>q(t),className:"text-red-500 hover:text-red-700",title:"Delete sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 15C8.55435 15 8.76631 14.9122 8.92259 14.7559C9.07887 14.5996 9.16667 14.3877 9.16667 14.1667V9.16667C9.16667 8.94565 9.07887 8.73369 8.92259 8.57741C8.76631 8.42113 8.55435 8.33333 8.33333 8.33333C8.11232 8.33333 7.90036 8.42113 7.74408 8.57741C7.5878 8.73369 7.5 8.94565 7.5 9.16667V14.1667C7.5 14.3877 7.5878 14.5996 7.74408 14.7559C7.90036 14.9122 8.11232 15 8.33333 15ZM16.6667 4.16667H13.3333V3.33333C13.3333 2.8913 13.1577 2.46738 12.8452 2.15482C12.5326 1.84226 12.1087 1.66667 11.6667 1.66667H8.33333C7.8913 1.66667 7.46738 1.84226 7.15482 2.15482C6.84226 2.46738 6.66667 2.8913 6.66667 3.33333V4.16667H3.33333C3.11232 4.16667 2.90036 4.25446 2.74408 4.41074C2.5878 4.56702 2.5 4.77899 2.5 5C2.5 5.22101 2.5878 5.43298 2.74408 5.58926C2.90036 5.74554 3.11232 5.83333 3.33333 5.83333H4.16667V16.6667C4.16667 17.1087 4.34226 17.5326 4.65482 17.8452C4.96738 18.1577 5.3913 18.3333 5.83333 18.3333H14.1667C14.6087 18.3333 15.0326 18.1577 15.3452 17.8452C15.6577 17.5326 15.8333 17.1087 15.8333 16.6667V5.83333H16.6667C16.8877 5.83333 17.0996 5.74554 17.2559 5.58926C17.4122 5.43298 17.5 5.22101 17.5 5C17.5 4.77899 17.4122 4.56702 17.2559 4.41074C17.0996 4.25446 16.8877 4.16667 16.6667 4.16667ZM8.33333 3.33333H11.6667V4.16667H8.33333V3.33333ZM14.1667 16.6667H5.83333V5.83333H14.1667V16.6667ZM11.6667 15C11.8877 15 12.0996 14.9122 12.2559 14.7559C12.4122 14.5996 12.5 14.3877 12.5 14.1667V9.16667C12.5 8.94565 12.4122 8.73369 12.2559 8.57741C12.0996 8.42113 11.8877 8.33333 11.6667 8.33333C11.4457 8.33333 11.2337 8.42113 11.0774 8.57741C10.9211 8.73369 10.8333 8.94565 10.8333 9.16667V14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15Z",fill:"currentColor"})})})]})]},a)})})}):e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 text-center shadow-sm",children:e.jsx("p",{className:"text-gray-500",children:"No sports added yet. Add your first sport below."})}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[m.duplicate&&e.jsx("div",{className:"rounded-lg border border-red-200 bg-red-50 p-3",children:e.jsx("p",{className:"text-sm text-red-600",children:m.duplicate})}),e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:h.sport_id,onChange:z,className:`w-full rounded-lg border ${m.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),u.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),m.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sport_id})]}),n&&e.jsxs(e.Fragment,{children:[((xe=n.sport_types)==null?void 0:xe.length)>0&&n.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(ce,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for all types/subtypes of this sport. Select a specific type only if you want different pricing for different types. If you coach multiple types/subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"type",value:h.type,onChange:z,className:`w-full rounded-lg border ${m.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"General Price (All Types)"}),n.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),n.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),m.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.type})]}):null,h.type&&((ye=n.sport_types)==null?void 0:ye.length)>0&&n.sport_types.some(t=>t.type===h.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(ce,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for the selected type. Select a specific subtype only if you want different pricing for different subtypes. If you coach multiple subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"sub_type",value:h.sub_type,onChange:z,className:`w-full rounded-lg border ${m.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),h.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((he=n.sport_types.find(t=>t.type===h.type))==null?void 0:he.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(fe=n.sport_types.find(t=>t.type===h.type))==null?void 0:fe.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),m.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:h.price,onChange:z,placeholder:"Enter price",className:`w-full rounded-lg border ${m.price?"border-red-500":"border-gray-300"} p-2`}),m.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.price})]}),e.jsx(we,{type:"button",onClick:ae,loading:x,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:x?"Adding Sport...":"Add Sport"})]})]})]})]}),e.jsx(Le,{isOpen:M,onClose:l,title:"Edit Sport",onPrimaryAction:o,submitting:J,primaryButtonText:J?"Updating...":"Update Sport",children:e.jsxs("div",{className:"space-y-4",children:[j.duplicate&&e.jsx("div",{className:"rounded-lg border border-red-200 bg-red-50 p-3",children:e.jsx("p",{className:"text-sm text-red-600",children:j.duplicate})}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{name:"sport_id",value:f.sport_id,onChange:O,className:`w-full rounded-lg border ${j.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),u.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),j.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.sport_id})]}),N&&e.jsxs(e.Fragment,{children:[((ge=N.sport_types)==null?void 0:ge.length)>0&&N.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-type-tooltip",size:16}),e.jsx(ce,{id:"edit-sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for all types/subtypes of this sport. Select a specific type only if you want different pricing for different types. If you coach multiple types/subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"type",value:f.type,onChange:O,className:`w-full rounded-lg border ${j.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"General Price (All Types)"}),N.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),N.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),j.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.type})]}):null,f.type&&((be=N.sport_types)==null?void 0:be.length)>0&&N.sport_types.some(t=>t.type===f.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-subtype-tooltip",size:16}),e.jsx(ce,{id:"edit-sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:f.sub_type,onChange:O,className:`w-full rounded-lg border ${j.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),f.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((je=N.sport_types.find(t=>t.type===f.type))==null?void 0:je.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(_e=N.sport_types.find(t=>t.type===f.type))==null?void 0:_e.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),j.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.sub_type})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Price ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"number",name:"price",value:f.price,onChange:O,placeholder:"Enter price",className:`w-full rounded-lg border ${j.price?"border-red-500":"border-gray-300"} p-2`}),j.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.price})]})]})]})}),e.jsx($e,{isOpen:K,onClose:Z,onDelete:Q,title:"Delete Sport",message:s?`Are you sure you want to delete ${((ve=u.find(t=>t.id===parseInt(s.sport_id)))==null?void 0:ve.name)||"this sport"}${s.type?` (${s.type}${s.sub_type?`, ${s.sub_type}`:""})`:""}? This action cannot be undone.`:"Are you sure you want to delete this sport? This action cannot be undone.",loading:_,buttonText:"Yes, Delete Sport",requireConfirmation:!1})]})},Ht=()=>{const{dispatch:h}=te.useContext(me),[P]=Ce(),[m,$]=r.useState("profile"),_=Se();r.useEffect(()=>{const x=P.get("tab");x&&$({"payment-methods":"payment-methods",profile:"profile",sports:"sports",membership:"membership",billing:"billing",invoices:"invoices"}[x]||"profile")},[P.get("tab")]);const R=[{label:"Profile details",value:"profile",icon:Re},{label:"Sports",value:"sports",icon:De},{label:"Bank Accounts",value:"payment-methods",icon:Fe},{label:"Invoices",value:"invoices",icon:Me}];return r.useEffect(()=>{h({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(Oe,{onBack:()=>_("/coach/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:R.map(x=>{const C=x.icon;return e.jsxs("button",{onClick:()=>{$(x.value),_(`/coach/profile?tab=${x.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${m===x.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(C,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:x.label})]},x.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[m==="profile"&&e.jsx(Je,{}),m==="sports"&&e.jsx(Xe,{}),m==="payment-methods"&&e.jsx(We,{}),m==="invoices"&&e.jsx(Qe,{})]})]})]})})};export{Ht as default};
