import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as s}from"./vendor-851db8c1.js";import{T as se,M as le,G as ne,A as oe,u as ce,c as ie,$ as x,t as re}from"./index-6cd5ea29.js";import{P as ue}from"./index-eb1bc208.js";import{_ as de}from"./lodash-91d5d207.js";import me from"./Skeleton-1e8bf077.js";import{D as ge}from"./DataTable-8a547681.js";import{S as fe}from"./react-select-c8303602.js";let pe=new se,E=new le;const Ne=({club:L})=>{var S;const{dispatch:A}=s.useContext(ne),{dispatch:D,state:d}=s.useContext(oe),{club:T}=ce(),l=(d==null?void 0:d.role)||localStorage.getItem("role"),c=L||T,[m,R]=s.useState([]),[r,F]=s.useState(null),[k,h]=s.useState(!1);console.log("clubs",m);const $=(()=>{const e=[{header:"Date",accessor:"create_at"},{header:"Time",accessor:"update_at"},{header:"Staff name",accessor:"user"}];return(l==="admin"||l==="admin_staff"||l==="club"||l==="staff")&&e.push({header:"Club Name/Admin",accessor:"clubs"}),e.push({header:"Action",accessor:"description"}),e})(),[M,I]=s.useState([]),[n,b]=s.useState(10),[B,G]=s.useState(0),[g,z]=s.useState(0),[U,q]=s.useState(!1),[w,H]=s.useState(!1),[y,f]=s.useState(!0),[K]=s.useState(!1),[V]=s.useState(!1),[,Y]=s.useState(!1),C=s.useRef(null),J=localStorage.getItem("user"),O=async()=>{if(l==="admin"){h(!0);try{E.setTable("clubs");const e=await E.callRestAPI({},"GETALL");R(e.list)}catch(e){console.error("Error fetching clubs:",e)}finally{h(!1)}}};function Q(){o(g-1,n)}function W(){o(g+1,n)}async function o(e,a,u=[],j=null){console.log("filters",u),f(!(V||K));try{const i=[];(l==="club"||l==="staff")&&(c!=null&&c.id)&&i.push(`courtmatchup_activity_logs.club_id,eq,${c.id}`);const _=j!==null?j:r;l==="admin"&&_&&i.push(`courtmatchup_activity_logs.club_id,eq,${_}`);const N=await pe.getPaginate("activity_logs",{page:e,limit:a,size:n,filter:[...u,...i],join:["clubs|club_id","user|user_id"]});N&&f(!1);const{list:te,limit:ae,num_pages:P,page:p}=N;I(te),b(ae),G(P),z(p),q(p>1),H(p+1<=P)}catch(i){f(!1),console.log("ERROR",i),re(D,i.message)}}const X=de.debounce(e=>{const a=e.target.value.trim(),u=a?[`courtmatchup_activity_logs.description,cs,${a}`]:[];a?o(1,n,u):o(1,n)},500),Z=e=>{const a=(e==null?void 0:e.value)||null;F(a),o(1,n,[],a)};s.useEffect(()=>{A({type:"SETPATH",payload:{path:"history"}}),l==="admin"&&O(),(l==="admin"||l==="admin_staff"||(l==="club"||l==="staff")&&(c!=null&&c.id))&&o(1,n)},[l,c==null?void 0:c.id]);const v=e=>{C.current&&!C.current.contains(e.target)&&Y(!1)};s.useEffect(()=>(document.addEventListener("mousedown",v),()=>{document.removeEventListener("mousedown",v)}),[]);const ee={create_at:e=>t.jsx("span",{className:"text-gray-600",children:new Date(e.create_at).toLocaleDateString("en-GB",{day:"2-digit",month:"long",year:"numeric"})}),update_at:e=>t.jsx("span",{className:"text-gray-600",children:new Date(e.update_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),user:e=>{const a=(e==null?void 0:e.user)||{};return t.jsx("span",{className:"text-gray-600",children:(a==null?void 0:a.id)==J?"You":`${(a==null?void 0:a.first_name)||"--"} ${(a==null?void 0:a.last_name)||"--"}`})},clubs:e=>{const a=(e==null?void 0:e.clubs)||{};return(e==null?void 0:e.club_id)===0?t.jsx("span",{className:"text-gray-600",children:"Admin"}):t.jsx("span",{className:"text-gray-600",children:(a==null?void 0:a.name)||"--"})},action:e=>t.jsx("span",{className:"text-gray-600",children:(e==null?void 0:e.action)||"--"})};return t.jsxs("div",{className:"h-screen px-8",children:[t.jsxs("div",{className:"flex flex-col !justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(ie,{className:"text-gray-500"})}),t.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search history",onChange:e=>X(e)})]}),t.jsxs("div",{className:"flex items-center gap-4",children:[l==="admin"&&t.jsx("div",{className:"w-48",children:t.jsx(fe,{className:"text-sm",classNamePrefix:"select",isClearable:!0,isSearchable:!0,isLoading:k,placeholder:"Club: All",value:r?{value:r,label:((S=m.find(e=>e.id===r))==null?void 0:S.name)||"Unknown Club"}:null,onChange:Z,options:m.map(e=>({value:e.id,label:e.name})),noOptionsMessage:()=>"No clubs found"})}),t.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:e=>{e.target.value===""?o(1,n):o(1,n,[`courtmatchup_activity_logs.action_type,cs,${e.target.value}`])},children:[t.jsx("option",{value:"",children:"Action type: All"}),t.jsx("option",{value:x.CREATE,children:"Create"}),t.jsx("option",{value:x.UPDATE,children:"Update"}),t.jsx("option",{value:x.DELETE,children:"Delete"})]})]})]}),y?t.jsx(me,{}):t.jsx("div",{className:"overflow-x-auto rounded-lg",children:t.jsx(ge,{columns:$,data:M,loading:y,renderCustomCell:ee,tableClassName:"min-w-full border-separate border-spacing-y-2",rowClassName:"bg-gray-100 px-4 py-3",cellClassName:"px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",loadingMessage:"Loading..."})}),t.jsx(ue,{currentPage:g,pageCount:B,pageSize:n,canPreviousPage:U,canNextPage:w,updatePageSize:e=>{b(e),o(1,e)},previousPage:Q,nextPage:W,gotoPage:e=>o(e,n)})]})};export{Ne as L};
