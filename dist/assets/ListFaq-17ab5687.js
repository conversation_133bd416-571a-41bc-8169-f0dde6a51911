import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as de,r as i}from"./vendor-851db8c1.js";import{M as fe,T as ue,G as ge,A as me,c as pe,Z as he,R as w,D as xe,t as T,i as ye,b as L}from"./index-6cd5ea29.js";import{c as Se,a as m}from"./yup-54691517.js";import{u as je}from"./react-hook-form-687afde5.js";import{o as Fe}from"./yup-2824f222.js";import{P as De}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ve from"./Skeleton-1e8bf077.js";import{L as we}from"./LoadingOverlay-87926629.js";import{D as be}from"./DataTable-8a547681.js";import{A as qe,F as Ae,E as Ee}from"./EditFaq-81698129.js";import{H as Ce}from"./HistoryComponent-9fdeea32.js";let c=new fe,_e=new ue;const Ne=[{header:"Subcategory",accessor:"subcategory_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Answer",accessor:"answer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Pe=({club:o})=>{const{dispatch:p,state:Te}=s.useContext(ge),{dispatch:b}=s.useContext(me),[k,M]=s.useState([]),[n,q]=s.useState(10),[Q,R]=s.useState(0),[Le,O]=s.useState(0),[d,$]=s.useState(1),[z,H]=s.useState(!1),[B,G]=s.useState(!1),[ke,I]=s.useState(!1),[h,x]=s.useState(!0),[K,Me]=s.useState(!1),[V,Qe]=s.useState(!1);s.useState(),de();const A=s.useRef(null),[E,y]=s.useState(!1),[f,S]=s.useState(null),[W,j]=i.useState(!1),[Z,u]=i.useState(!1),[J,C]=i.useState(null),[U,F]=i.useState(!1),[D,v]=i.useState(null),[X,_]=i.useState(!1);i.useState([]),i.useState([]);const Y=Se({id:m(),email:m(),role:m(),status:m()});je({resolver:Fe(Y)});function ee(){r(d-1,n)}function te(){r(d+1,n)}async function r(e,l,P={},g=[]){x(!(V||K));try{const a=await _e.getPaginate("faq",{page:e,limit:l,filter:[...g,`club_id,cs,${o==null?void 0:o.id}`],join:["faq_subcategory|subcategory_id",`${c._project_id}_faq_category|faq_subcategory.category_id,id`],size:n});a&&(x(!1),M(a.list),q(a.limit),R(a.num_pages),$(a.page),O(a.total),H(a.page>1),G(a.page+1<=a.num_pages))}catch(a){x(!1),console.log("ERROR",a),T(b,a.message)}}s.useEffect(()=>{p({type:"SETPATH",payload:{path:"faq"}}),o!=null&&o.id&&r(1,n,{})},[o==null?void 0:o.id]);const N=e=>{A.current&&!A.current.contains(e.target)&&I(!1)};s.useEffect(()=>(document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}),[]);const se=e=>{S(e),y(!0)},ae={"":e=>t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>se(e),children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),type:e=>{var l;return t.jsx("span",{className:"capitalize",children:((l=ye.find(P=>{var g;return P.value==((g=e==null?void 0:e.booking)==null?void 0:g.reservation_type)}))==null?void 0:l.label)||"--"})},question:e=>t.jsx("span",{className:"capitalize",children:(e==null?void 0:e.faq_subcategory.name)||"--"}),answer:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.answer)||"--"}),category:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_category.name)||"--"}),subcategory_id:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_subcategory.name)||"--"})},oe=()=>{r(1,n)},ne=e=>{C(e),u(!0)},re=async()=>{if(await r(d,n),f&&E)try{c.setTable("faq");const e=await c.callRestAPI({id:f.id,join:["faq_subcategory|subcategory_id",`${c._project_id}_faq_category|faq_subcategory.category_id,id`]},"GET");e&&e.model&&S(e.model)}catch(e){console.error("Error refreshing FAQ details:",e)}u(!1),C(null)},ie=e=>{v(e),F(!0)},le=async()=>{if(D){_(!0);try{c.setTable("faq"),await c.callRestAPI({id:D.id},"DELETE"),L(p,"FAQ deleted successfully",3e3,"success"),await r(d,n),f&&f.id===D.id&&(y(!1),S(null)),F(!1),v(null)}catch(e){console.error("Error deleting FAQ:",e),L(p,e.message||"Error deleting FAQ",3e3,"error"),T(b,e.message)}finally{_(!1)}}},ce=()=>{F(!1),v(null)};return t.jsxs("div",{className:"h-screen px-8",children:[h&&t.jsx(we,{}),t.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsx("div",{className:"flex items-center gap-4",children:t.jsxs("form",{className:"relative flex flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(pe,{className:"text-gray-500"})}),t.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search FAQ by answer",onChange:e=>{const l=e.target.value.trim();l?r(1,n,{},[`answer,cs,${l}`]):r(1,n)}})]})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("button",{onClick:()=>j(!0),className:"inline-flex max-w-fit items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(Ce,{activityType:he.faq,emptyMessage:"No FAQ history found",title:"FAQ History"})]})]}),t.jsx(w,{isOpen:W,onClose:()=>j(!1),title:"Add New FAQ",showFooter:!1,children:t.jsx(qe,{onClose:()=>j(!1),onSuccess:oe,club:o})}),t.jsx(w,{isOpen:E,onClose:()=>y(!1),title:"FAQ Details",showFooter:!1,children:t.jsx(Ae,{faq:f,onEdit:ne,onDelete:ie})}),t.jsx(w,{isOpen:Z,onClose:()=>u(!1),title:"Edit FAQ",showFooter:!1,children:t.jsx(Ee,{faq:J,onClose:()=>u(!1),onSuccess:re,club:o})}),h?t.jsx(ve,{}):t.jsx("div",{className:"overflow-x-auto",children:t.jsx(be,{columns:Ne,data:k,loading:h,renderCustomCell:ae,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available"})}),t.jsx(De,{currentPage:d,pageCount:Q,pageSize:n,canPreviousPage:z,canNextPage:B,updatePageSize:e=>{q(e),r(1,e)},previousPage:ee,nextPage:te,gotoPage:e=>r(e,n)}),t.jsx(xe,{isOpen:U,onClose:ce,onDelete:le,title:"Delete FAQ",message:"Are you sure you want to delete this FAQ? This action cannot be undone.",loading:X,buttonText:"Delete FAQ",requireConfirmation:!0})]})},Ue=Pe;export{Ue as L};
