import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b as s}from"./vendor-851db8c1.js";import{_ as n}from"./cal-heatmap-cf010ec4.js";import{T as p}from"./index-dd76972a.js";import"./index-530e68c6.js";import{T as d,u as f,a as c,L as u,I as x}from"./index-6cd5ea29.js";import{R as g}from"./index.esm-c561e951.js";import{S as h}from"./SupportChatBot-ff7a938d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const w=i.lazy(()=>n(()=>import("./UserHeader-fc7947f4.js"),["assets/UserHeader-fc7947f4.js","assets/@nivo/heatmap-ba1ecfff.js","assets/@craftjs/core-d3c11b68.js","assets/vendor-851db8c1.js","assets/@fortawesome/react-fontawesome-13437837.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/index-6cd5ea29.js","assets/react-confirm-alert-cd7ccfe7.js","assets/@tanstack/react-query-20158223.js","assets/@stripe/stripe-js-6b714a86.js","assets/moment-a9aaa855.js","assets/cal-heatmap-cf010ec4.js","assets/react-icons-51bc3cff.js","assets/smoothscroll-polyfill-a5c0a116.js","assets/date-fns-cca0f4f7.js","assets/lodash-91d5d207.js","assets/numeral-ea653b2a.js","assets/@stripe/react-stripe-js-64f0e61f.js","assets/react-hook-form-687afde5.js","assets/react-select-c8303602.js","assets/@mantine/core-8cbffb6d.js","assets/@emotion/react-89b506c3.js","assets/@emotion/cache-9a5b99cd.js","assets/@emotion/utils-8a8f62c5.js","assets/@emotion/serialize-460cad7f.js","assets/@uppy/dashboard-4a19149e.js","assets/@fullcalendar/core-8ccc1ac4.js","assets/core-b9802b0d.css","assets/@uppy/core-0760343f.js","assets/@uppy/aws-s3-c5961f7a.js","assets/@uppy/compressor-11f993e4.js","assets/@headlessui/react-a5400090.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/index-4d5b5280.css","assets/DownloadAppModal-4cded1f8.js","assets/react-qr-code-4a7125ac.js"]));new d;const j=({children:m})=>{const[r,o]=s.useState(!1),{club:e}=f();return s.useEffect(()=>{c(e==null?void 0:e.club_logo)},[e==null?void 0:e.club_logo]),s.useEffect(()=>{const a=l=>{r&&!l.target.closest("#sidebar")&&!l.target.closest("#hamburger-btn")&&o(!1)};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[r]),t.jsx(u,{children:t.jsxs("div",{className:"flex w-full max-w-full flex-col bg-white",children:[t.jsxs("div",{className:"flex min-h-screen w-full max-w-full",children:[t.jsx("button",{id:"hamburger-btn",className:"fixed left-4 top-4 z-[999] flex h-10 w-10 items-center justify-center rounded-lg bg-white shadow-lg transition-all hover:bg-gray-50 focus:outline-none lg:hidden",onClick:()=>o(!r),children:r?t.jsx(x,{className:"h-6 w-6 text-gray-600"}):t.jsx(g,{className:"h-6 w-6 text-gray-600"})}),t.jsx("div",{id:"sidebar",className:`fixed inset-y-0 left-0 z-40 w-fit max-w-[240px] transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 ${r?"translate-x-0":"-translate-x-full"}`,children:t.jsx(w,{clubName:e==null?void 0:e.name,clubLogo:e==null?void 0:e.club_logo})}),r&&t.jsx("div",{className:"fixed inset-0 z-30 bg-black/20 backdrop-blur-sm lg:hidden",onClick:()=>o(!1)}),t.jsxs("div",{className:"w-full overflow-hidden",children:[t.jsx(p,{clubLogo:e==null?void 0:e.club_logo}),t.jsx(i.Suspense,{fallback:t.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:t.jsx("div",{className:"h-full w-full overflow-y-auto overflow-x-hidden bg-gray-50",children:m})})]})]}),t.jsx(h,{})]})})},oe=i.memo(j);export{oe as default};
