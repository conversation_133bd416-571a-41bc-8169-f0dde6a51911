import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,f as Ve,b as Ze}from"./vendor-851db8c1.js";import"./BottomDrawer-598ec255.js";import{M as ze,T as Xe,G as et,u as tt,J as at,aP as le,b as O,aC as st,e as nt,ai as de,d as ot,al as rt,aT as ce,Z as it,$ as lt}from"./index-6cd5ea29.js";import"./SelectionOption-658322e6.js";import{B as dt}from"./BackButton-11ba52b2.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{T as ct}from"./TimeSlots-8e862e39.js";import{A as ut}from"./AddPlayers-fec681af.js";import{S as mt}from"./SportTypeSelection-5dc32d74.js";import{C as pt}from"./Calendar-35bce269.js";import{B as gt}from"./ReservationSummary-50e44b54.js";import{S as ft}from"./react-select-c8303602.js";import{g as yt}from"./customThresholdUtils-f40b07d5.js";import{f as ht}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./SelectionOptionsCard-30c39f7f.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";let k=new ze,Y=new Xe;const Sa=({})=>{var ee,te,ae,se,ne,oe,re,ie;const[C,ue]=o.useState(null);o.useState(null),o.useState(null);const[y,me]=o.useState(null),[w,G]=o.useState(new Date),[xt,pe]=o.useState(null),[g,ge]=o.useState([]),[fe,ye]=o.useState([]),[n,he]=o.useState(null),[D,xe]=o.useState(null),[R,ve]=o.useState(null),[N,vt]=o.useState(0),[St,U]=o.useState(0),[J,bt]=o.useState([]),[A,Se]=o.useState([]),{state:wt,dispatch:B}=o.useContext(et),[be,Q]=o.useState(!1),[Nt,we]=o.useState({from:null,until:null}),[I,Ne]=o.useState([{from:null,until:null}]),[_e,je]=o.useState(""),[m,F]=o.useState([]),[Te,ke]=o.useState(!1),[_,j]=o.useState(1),[Ce,H]=o.useState(!1),[K,De]=o.useState(""),[q,L]=o.useState(1),[W,Re]=o.useState(3.5),[V,Be]=o.useState(3.5),[Ie,_t]=o.useState(null),[Fe,jt]=o.useState(!1),[h,M]=o.useState(1),[P,qe]=o.useState([]),[r,Z]=o.useState(null),{user_subscription:f,user_permissions:x,club_membership:T,courts:$e}=tt(),a=o.useMemo(()=>!(f!=null&&f.planId)||!(T!=null&&T.length)?null:T.find(e=>e.plan_id===f.planId),[f,T]),$=o.useMemo(()=>{var l,p;if(((l=a==null?void 0:a.advance_booking_enabled)==null?void 0:l.buddy)===!1){const u=new Date;return u.setFullYear(u.getFullYear()+10),u}const e=((p=a==null?void 0:a.advance_booking_days)==null?void 0:p.buddy)||10,s=new Date,i=new Date;return i.setDate(s.getDate()+e),i},[a]),[v,S]=o.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),z=Ve(),d=localStorage.getItem("user"),Ee=async()=>{try{const e=await Y.getOne("user",d,{}),s=await k.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${e.model.club_id}`,{},"GET");Se(s.sports),he(s.model)}catch(e){console.error(e)}},Oe=async()=>{try{const e=await Y.getList("user",{filter:["role,cs,user"]});ge(e.list)}catch(e){console.error(e)}},Ae=async()=>{try{const e=await k.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");ye(e.groups)}catch(e){console.error(e)}},Le=async()=>{try{const e=parseInt(d);if(!d||isNaN(e)){console.error("Invalid user_id for fetching family members:",d);return}const s=await Y.getList("user",{filter:[`guardian,eq,${e}`,"role,cs,user"]});qe(s.list)}catch(e){console.error("Error fetching family members:",e)}};o.useEffect(()=>{(async()=>(H(!0),await Ee(),await Oe(),await Ae(),await Le(),H(!1)))()},[]),Ze.useEffect(()=>{at({path:"/user/create-request",clubName:n==null?void 0:n.name,favicon:n==null?void 0:n.club_logo,description:"Create Request"})},[n==null?void 0:n.club_logo]);const Me=()=>{G(new Date(w.setMonth(w.getMonth()-1)))},Pe=()=>{G(new Date(w.setMonth(w.getMonth()+1)))},Ye=async()=>{var i,l;if(!(f!=null&&f.planId)){S({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to use the Find a Buddy feature",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(x!=null&&x.allowBuddy)){S({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${x==null?void 0:x.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(y>$&&((i=a==null?void 0:a.advance_booking_enabled)==null?void 0:i.buddy)!==!1){const p=((l=a==null?void 0:a.advance_booking_days)==null?void 0:l.buddy)||10;S({isOpen:!0,title:"Date Selection Error",message:`Your membership plan only allows creating buddy requests ${p} days in advance. Please select a valid date.`,type:"warning"});return}if(!C||!y||!I.length||!D||!R){S({isOpen:!0,title:"Incomplete Details",message:"Please fill in all required fields",type:"warning"});return}const{start_time:e,end_time:s}=rt(I);Q(!0);try{const p=new Date(y),u=I.filter(b=>b.from&&b.until).map(b=>({start_time:ce(b.from),end_time:ce(b.until)}));if(u.length===0){S({isOpen:!0,title:"Time Slots Required",message:"Please select at least one valid time slot",type:"warning"});return}const E={sport_id:C,slots:u,ntrp:W,max_ntrp:V,num_players:q,num_needed:_,type:D,sub_type:R,need_coach:1,notes:K,date:ht(p,"yyyy-MM-dd"),start_time:e,end_time:s,player_ids:m.map(b=>b.id),primary_player_id:(r==null?void 0:r.id)||parseInt(d)},We=await k.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/create-request",E,"POST");k.setTable("activity_logs");const Tt=await k.callRestAPI({activity_type:it.find_a_buddy,user_id:d,club_id:n==null?void 0:n.id,action_type:lt.CREATE,data:JSON.stringify(E),description:"Created a find a buddy request"},"POST");We.error||(O(B,"Request created successfully",3e3,"success"),z("/user/find-a-buddy"))}catch(p){console.error(p),S({isOpen:!0,title:"Request Error",message:p.message||"Error creating buddy request",type:"error"})}finally{Q(!1)}},Ge=e=>{we(e),pe(e.from)},Ue=e=>{Ne(e)};o.useEffect(()=>{if(N&&(m!=null&&m.length)){const e=N*(m==null?void 0:m.length),s=le(n==null?void 0:n.fee_settings,e),i=(n==null?void 0:n.club_fee)||0;U(e+s+i)}else{const e=le(n==null?void 0:n.fee_settings,N),s=(n==null?void 0:n.club_fee)||0;U(N+e+s)}},[N,m,n==null?void 0:n.fee_settings,n==null?void 0:n.club_fee]);const Je=()=>{j(e=>Math.min(e+1,c))},Qe=()=>{j(e=>Math.max(e-1,0))},He=()=>{M(2)},Ke=e=>{F(s=>s.some(l=>l.id===e.id)?s.filter(l=>l.id!==e.id):[...s,e])},X=e=>{const s=e.value||e;(s==null?void 0:s.id)!==(r==null?void 0:r.id)&&(Z(s),F(i=>{const l=i.filter(u=>u.id!==(r==null?void 0:r.id));if(l.some(u=>u.id===s.id)){const u=l.filter(E=>E.id!==s.id);return[s,...u]}else return[s,...l]}))},c=yt(n==null?void 0:n.custom_request_threshold,C,D,R,4,A);return o.useEffect(()=>{if(g.length>0&&!r){const e=g.find(s=>s.id===parseInt(d));e&&Z(e)}},[g,r,d]),o.useEffect(()=>{m.length>c&&(console.log(`Clearing selected players: current ${m.length} exceeds new threshold ${c}`),F([]),j(1),L(1),O(B,`Player selection cleared. New maximum is ${c} players. Please select players again.`,4e3,"warning"))},[c]),o.useEffect(()=>{_>c&&j(Math.min(_,c)),q>c&&L(Math.min(q,c))},[c]),t.jsxs("div",{className:"",children:[t.jsx(st,{isOpen:v.isOpen,onClose:()=>S({...v,isOpen:!1}),title:v.title,message:v.message,actionButtonText:v.actionButtonText,actionButtonLink:v.actionButtonLink,type:v.type}),Ce&&t.jsx(nt,{}),t.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[h===1&&t.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),h===2&&t.jsx("div",{className:" ",children:"Step 2 • Reserving details"}),h===3&&t.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),t.jsxs("div",{className:"p-4",children:[t.jsx(dt,{onBack:()=>{h===1?z(-1):M(h===2?1:2)}}),h===1&&t.jsx("div",{children:t.jsx("div",{className:" p-4",children:t.jsx("div",{className:"space-y-6",children:t.jsx("div",{className:"mx-auto max-w-7xl p-4",children:t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(mt,{sports:A,userPermissions:x,courts:$e,filterMode:"buddy",onSelectionChange:({sport:e,type:s,subType:i})=>{ue(e),xe(s),ve(i)}}),t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((ee=a==null?void 0:a.advance_booking_enabled)==null?void 0:ee.buddy)===!1?t.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can create a find-a-buddy request for any future date."}):((te=a==null?void 0:a.advance_booking_days)==null?void 0:te.buddy)!==void 0&&t.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can create a find-a-buddy request up to"," ",(ae=a==null?void 0:a.advance_booking_days)==null?void 0:ae.buddy," ",((se=a==null?void 0:a.advance_booking_days)==null?void 0:se.buddy)===1?"day":"days"," ","in advance."]}),t.jsx(pt,{currentMonth:w,selectedDate:y,onDateSelect:e=>{var s,i;if(e>$&&((s=a==null?void 0:a.advance_booking_enabled)==null?void 0:s.buddy)!==!1){const l=((i=a==null?void 0:a.advance_booking_days)==null?void 0:i.buddy)||10;O(B,`Your membership plan only allows booking ${l} days in advance`,3e3,"warning");return}me(e)},onPreviousMonth:Me,onNextMonth:Pe,daysOff:n!=null&&n.days_off?JSON.parse(n.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:$,disabledDateMessage:((ne=a==null?void 0:a.advance_booking_enabled)==null?void 0:ne.buddy)===!1?"You can book for any future date":`Your membership plan only allows booking ${((oe=a==null?void 0:a.advance_booking_days)==null?void 0:oe.buddy)||10} days in advance`})]}),t.jsx(ct,{isLoading:Fe,selectedDate:y,timeRange:J,onTimeClick:Ge,onTimeSlotsChange:Ue,onNext:()=>{var e,s;if(y>$&&((e=a==null?void 0:a.advance_booking_enabled)==null?void 0:e.buddy)!==!1){const i=((s=a==null?void 0:a.advance_booking_days)==null?void 0:s.buddy)||10;O(B,`Your membership plan only allows booking ${i} days in advance`,3e3,"warning");return}He()},nextButtonText:"Next: Players",startHour:0,clubTimes:n!=null&&n.times?JSON.parse(n.times):[],endHour:24,interval:30,isTimeSlotAvailable:()=>!0,multipleSlots:!0})]})})})})}),h===2&&t.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[t.jsx(gt,{selectedSport:C,sports:A,selectedType:D,selectedSubType:R,selectedDate:y,selectedTimes:J,timeSlots:I}),t.jsxs("div",{className:"space-y-4",children:[P.length>0&&t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[t.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Request for"}),t.jsx(ft,{className:"w-full text-sm",options:[{value:g.find(e=>e.id===parseInt(d)),label:`${(re=g.find(e=>e.id===parseInt(d)))==null?void 0:re.first_name} ${(ie=g.find(e=>e.id===parseInt(d)))==null?void 0:ie.last_name} (You)`},...P.map(e=>({value:e,label:`${e.first_name} ${e.last_name} (${e.family_role||"Family Member"})`}))],onChange:X,value:r?{value:r,label:r.id===parseInt(d)?`${r.first_name} ${r.last_name} (You)`:`${r.first_name} ${r.last_name} (${r.family_role||"Family Member"})`}:null,placeholder:"Select who this request is for",isSearchable:!1}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Choose who this buddy request is for"})]}),t.jsx(ut,{searchQuery:_e,setSearchQuery:je,selectedPlayers:m,setSelectedPlayers:F,players:g,groups:fe,selectedGroup:Ie,isFindBuddyEnabled:Te,setIsFindBuddyEnabled:ke,playersNeeded:_,handleIncrement:Je,handleDecrement:Qe,onPlayerToggle:Ke,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1,maximumPlayers:c,familyMembers:P,currentUser:r,onCurrentUserChange:X,userProfile:g.find(e=>e.id===parseInt(d))})]}),t.jsxs("div",{className:"h-fit rounded-xl bg-white shadow-5",children:[t.jsx("div",{className:"rounded-xl bg-gray-50 p-4 text-center",children:t.jsx("h2",{className:"text-base font-medium",children:"Other details"})}),t.jsx("div",{className:"p-4",children:t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"mb-2 text-base font-medium",children:"My group NTRP score"}),t.jsxs("div",{className:"flex gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Min"}),t.jsx("select",{value:W,onChange:e=>Re(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:de.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Max"}),t.jsx("select",{value:V,onChange:e=>Be(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:de.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})})]})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players playing"}),t.jsx("div",{className:"",children:t.jsx("select",{value:q,onChange:e=>L(e.target.value),className:"w-fit appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(c)].map((e,s)=>t.jsx("option",{value:s+1,children:s+1},s))})})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players needed"}),t.jsx("div",{className:"relative",children:t.jsx("select",{value:_,onChange:e=>j(e.target.value),className:"w-full appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(c)].map((e,s)=>t.jsx("option",{value:s+1,children:s+1},s))})})]}),t.jsxs("div",{children:[t.jsxs("h3",{className:"mb-2 text-base font-medium",children:["Short bio"," ",t.jsx("span",{className:"text-gray-400",children:"(Optional)"})]}),t.jsx("textarea",{className:"w-full rounded-xl border border-gray-200 p-3 focus:border-gray-200 focus:ring-0",rows:"3",value:K,onChange:e=>De(e.target.value)})]}),t.jsx(ot,{loading:be,onClick:Ye,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Submit request"})]})})]})]})]})]})};export{Sa as default};
