import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,f as At,b as ce,L as Ye}from"./vendor-851db8c1.js";import{M as Et,T as Lt,G as qt,A as Ft,u as Ot,J as Yt,b as S,ac as Y,aO as Ht,aP as ee,aQ as Vt,aC as Ut,e as He,S as H,aR as Jt,d as Gt,aH as Dt,_ as Zt,Z as Qt,$ as Wt}from"./index-6cd5ea29.js";import{B as Kt}from"./BackButton-11ba52b2.js";import{T as zt}from"./TimeSlots-8e862e39.js";import{A as Xt}from"./AddPlayers-fec681af.js";import{C as es}from"./Calendar-35bce269.js";import{S as ts}from"./SportTypeSelection-5dc32d74.js";import{C as Ve}from"./ReservationSummary-50e44b54.js";import{h as V}from"./moment-a9aaa855.js";import{S as ss}from"./react-select-c8303602.js";import{g as ns}from"./customThresholdUtils-f40b07d5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";let U=new Et,Ue=new Lt;const Xs=({})=>{var $e,Pe,Be,Ie,Re,Ae,Ee,Le,qe,Fe,Oe;const[p,Je]=i.useState(null),[w,Ge]=i.useState(null),[J,de]=i.useState(new Date),[De,Ze]=i.useState([]),[Qe,We]=i.useState([]),[G,Ke]=i.useState(0),[Z,ze]=i.useState(0),[is,me]=i.useState(!1),[R,E]=i.useState("main"),[x,Q]=i.useState([]),[ue,Xe]=i.useState(!1),[te,se]=i.useState(1),[ge,et]=i.useState(!1),[pe,tt]=i.useState(3.5),[fe,st]=i.useState(3.5),[he,nt]=i.useState(""),[it,at]=i.useState([]),[_,ye]=i.useState(null),[rt,xe]=i.useState(!1),[g,ot]=i.useState(null),[f,lt]=i.useState(null),[h,ve]=i.useState([]),[ne,_e]=i.useState([]),[$,we]=i.useState([]),[v,ie]=i.useState(null),[P,je]=i.useState(30),[ct,dt]=i.useState(null),[mt,ut]=i.useState(null),[gt,be]=i.useState(!1),[pt,ft]=i.useState(null),[Ne,ht]=i.useState(null),[yt,q]=i.useState(!1),[A,T]=i.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),[xt,W]=i.useState(null),[Se,Ce]=i.useState(!1),{state:as,dispatch:j}=i.useContext(qt);i.useContext(Ft);const{club:s,pricing:ke,sports:L,loading:rs,user_subscription:B,user_permissions:C,user_profile:b,club_membership:F,courts:D}=Ot(),vt=At();console.log("club",s);const K=localStorage.getItem("user"),c=ce.useMemo(()=>!(B!=null&&B.planId)||!(F!=null&&F.length)?null:F.find(t=>t.plan_id===B.planId),[B,F]);console.log("userMembershipPlan",c),console.log("user_subscription",B),console.log("club_membership",F);const z=ce.useMemo(()=>{var l,r;if(((l=c==null?void 0:c.advance_booking_enabled)==null?void 0:l.court)===!1){const d=new Date;return d.setFullYear(d.getFullYear()+10),d}const t=((r=c==null?void 0:c.advance_booking_days)==null?void 0:r.court)||10,n=new Date,o=new Date;return o.setDate(n.getDate()+t),o},[c]),_t=async()=>{try{const t=await Ue.getList("user",{filter:["role,cs,user",`club_id,cs,${s==null?void 0:s.id}`]});Ze(t.list)}catch(t){console.error(t)}},wt=async()=>{try{const t=parseInt(K);if(!K||isNaN(t)){console.error("Invalid user_id for fetching family members:",K);return}const n=await Ue.getList("user",{filter:[`guardian,eq,${t}`,"role,cs,user"]});at(n.list)}catch(t){console.error("Error fetching family members:",t)}},jt=async()=>{try{const t=await U.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");We(t.groups)}catch(t){console.error(t)}};i.useEffect(()=>{(async()=>(xe(!0),await jt(),await wt(),xe(!1)))()},[K]),i.useEffect(()=>{s!=null&&s.id&&_t()},[s==null?void 0:s.id]),i.useEffect(()=>{b&&!_&&ye(b)},[b,_]),ce.useEffect(()=>{Yt({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Reserve a court"})},[s==null?void 0:s.club_logo]),i.useEffect(()=>{},[R]);const bt=()=>{de(new Date(J.setMonth(J.getMonth()-1)))},Nt=()=>{de(new Date(J.setMonth(J.getMonth()+1)))},m=L==null?void 0:L.find(t=>t.id===p),k=ns(s==null?void 0:s.custom_request_threshold,p,g,f,4,L);i.useEffect(()=>{p&&(console.log("Selected Sport:",p),console.log("Selected Type:",g),console.log("Selected SubType:",f),console.log("Max Players Allowed:",k))},[p,g,f,k]),i.useEffect(()=>{x.length>k&&(console.log(`Clearing selected players: current ${x.length} exceeds new threshold ${k}`),Q([]),se(1),S(j,`Player selection cleared. New maximum is ${k} players. Please select players again.`,4e3,"warning"))},[k]),i.useEffect(()=>{te>k-x.length&&se(Math.max(0,k-x.length))},[k,x.length]);const{start_time:ae,end_time:re,duration:oe}=Y(h),St=async()=>{var l,r,d;if(!p||!(s!=null&&s.id))return;const t=(l=m==null?void 0:m.sport_types)==null?void 0:l.some(a=>a.type&&a.type.trim()!==""),n=(r=m==null?void 0:m.sport_types)==null?void 0:r.find(a=>a.type===g),o=(d=n==null?void 0:n.subtype)==null?void 0:d.some(a=>a&&a.trim()!=="");if(!p||t&&!g||t&&o&&!f){W(null);return}Ce(!0);try{const a=new URLSearchParams;a.append("sport_id",p),g&&a.append("type",g),f&&a.append("subtype",f);const y=await U.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/${s.id}?${a.toString()}`,{},"GET");if(y&&!y.error){let u=[];y.qualifying_courts&&(u=y.qualifying_courts.filter(I=>{try{return JSON.parse(I.court_settings||"{}").allow_reservation!==!1}catch(M){return console.warn(`Failed to parse court_settings for court ${I.id}:`,M),!1}}));const N={...y,qualifying_courts:u};W(N),je(y.min_booking_time||30)}else console.error("Error fetching availability:",y==null?void 0:y.message),W(null)}catch(a){console.error("Error fetching availability:",a),W(null)}finally{Ce(!1)}},Te=async(t,n,o)=>{try{const l=V(t).format("YYYY-MM-DD"),r=new URLSearchParams;return r.append("sport_id",p||""),r.append("type",g||""),r.append("subtype",f||""),r.append("date",l),r.append("court_id",""),r.append("start_time",n),r.append("end_time",o),await U.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/${s==null?void 0:s.id}?${r.toString()}`,{},"GET")}catch(l){return console.error("Error getting qualifying courts:",l),{qualifying_courts:[],availability:[],min_booking_time:30}}},Ct=async(t,n,o)=>{if(!t||!n||!o)return[];const l=await Te(t,n,o);if(l.error)return console.error("API Error:",l.message),[];const d=(l.qualifying_courts||[]).filter(a=>{try{return JSON.parse(a.court_settings||"{}").allow_reservation!==!1}catch(y){return console.warn(`Failed to parse court_settings for court ${a.id}:`,y),!1}});return we(d),d},kt=()=>!(!w||!p||ne.length===0),Tt=i.useCallback(({sport:t,type:n,subType:o})=>{Je(t),ot(n),lt(o)},[]);i.useEffect(()=>{St()},[p,g,f,s==null?void 0:s.id,m]),i.useEffect(()=>{if(D&&D.length>0){let t=[...D];p&&(t=t.filter(n=>n.sport_id&&n.sport_id.toString()===p.toString())),g&&(t=t.filter(n=>n.type===g)),f&&(t=t.filter(n=>n.sub_type===f)),t=t.filter(n=>{try{return JSON.parse(n.court_settings||"{}").allow_reservation!==!1}catch(o){return console.warn(`Failed to parse court_settings for court ${n.id}:`,o),!1}}),_e(t)}else _e([])},[D,p,g,f]);const Mt=async()=>{var l,r,d;const t=(l=m==null?void 0:m.sport_types)==null?void 0:l.some(a=>a.type&&a.type.trim()!==""),n=(r=m==null?void 0:m.sport_types)==null?void 0:r.find(a=>a.type===g),o=(d=n==null?void 0:n.subtype)==null?void 0:d.some(a=>a&&a.trim()!=="");if(!p||t&&!g||t&&o&&!f||!w||!ae||!re){S(j,"Please select all required fields",3e3,"error");return}me(!0);try{const a=await U.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:Z},"POST");if(a.error)throw new Error(a.message||"Failed to create payment intent");dt(a.client_secret),ut(a.payment_intent);const y=V(w).format("YYYY-MM-DD"),u={sport_id:p,type:g,sub_type:f,date:y,start_time:ae,end_time:re,duration:oe,reservation_type:Dt.court,price:Z,player_ids:x.map(M=>M.id),primary_player_id:(_==null?void 0:_.id)||(b==null?void 0:b.id),buddy_details:null,payment_status:0,payment_intent:a.payment_intent,service_fee:ee(s==null?void 0:s.fee_settings,G),club_fee:s==null?void 0:s.club_fee,players_needed:te,min_ntrp:pe,max_ntrp:fe,note:he};let N=null;if((s==null?void 0:s.allow_user_court_selection)===1&&v)N=v;else{const M=await Ct(w,ae,re);M.length>0&&(N=M.sort((le,Rt)=>(le.min_booking_time||30)-(Rt.min_booking_time||30))[0].id)}N&&(u.court_id=N);const I=await U.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",u,"POST");return await Zt(U,{user_id:localStorage.getItem("user"),activity_type:Qt.court_reservation,action_type:Wt.CREATE,data:u,club_id:s==null?void 0:s.id,description:`${b==null?void 0:b.first_name} ${b==null?void 0:b.last_name} created a court reservation`}),I.error||S(j,"Reservation created successfully",3e3,"success"),ht(I.reservation_id),I.booking_id}catch(a){console.error(a),T({isOpen:!0,title:"Payment Error",message:a.message==="Something went wrong"?"Unable to process payment at this time. Please try again.":a.message||"Error creating reservation",actionButtonText:"Try Again",onAction:()=>{T({isOpen:!1})},type:"error"})}finally{me(!1)}},Me=t=>{Q(n=>n.some(l=>l.id===t.id)?n.filter(l=>l.id!==t.id):[...n,t])},$t=t=>{const n=t.value||t;(n==null?void 0:n.id)!==(_==null?void 0:_.id)&&(ye(n),Q(o=>{const l=o.filter(d=>d.id!==(_==null?void 0:_.id));if(l.some(d=>d.id===n.id)){const d=l.filter(a=>a.id!==n.id);return[n,...d]}else return[n,...l]}))},Pt=async()=>{q(!0);try{const t=V(h[0].from,"h:mm A").format("HH:mm"),n=V(h[0].until,"h:mm A").format("HH:mm"),o=await Te(w,t,n);if(o.error){S(j,o.message||"No courts available that match the selected criteria",4e3,"warning"),q(!1);return}const l=o.qualifying_courts||[],r=o.min_booking_time||30,d=l.filter(u=>{try{return JSON.parse(u.court_settings||"{}").allow_reservation!==!1}catch(N){return console.warn(`Failed to parse court_settings for court ${u.id}:`,N),!1}});if(d.length===0){S(j,"No courts are available for the selected time slot. Please choose a different time.",4e3,"warning"),q(!1);return}if(we(d),je(r),d.length>0){const{duration:u}=Y(h),N=u*60,I=d.filter(M=>{const X=M.min_booking_time||r;return N>=X});if(I.length>0){const M=I.sort((X,le)=>(X.min_booking_time||30)-(le.min_booking_time||30));ie(M[0].id)}}const{duration:a}=Y(h);if(a*60>=r){q(!1),E("players");return}S(j,`Minimum booking time requirement is ${Math.floor(r/60)} hour${Math.floor(r/60)!==1?"s":""}${r%60!==0?` ${r%60} minutes`:""}. Please select a longer time slot.`,5e3,"warning"),q(!1)}catch(t){console.error("Error checking court availability:",t),S(j,"Error checking court availability. Please try again.",3e3,"error"),q(!1)}};i.useEffect(()=>{if(x!=null&&x.length&&p&&g&&f&&(h!=null&&h.length)&&oe){const t=Ht({pricing:ke,sportId:p,type:g,subType:f,duration:oe,selectedTime:h[0]}),n=ee(s==null?void 0:s.fee_settings,t);Ke(t),ze(t+n)}},[x,p,g,f,h,ke,s==null?void 0:s.fee_settings]);const Bt=async()=>{var l,r,d,a,y;if(!(B!=null&&B.planId)){T({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to reserve courts",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(C!=null&&C.allowCourt)){T({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${C==null?void 0:C.planName}) does not include court reservations. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(w>z&&((l=c==null?void 0:c.advance_booking_enabled)!=null&&l.court)){const u=`Your membership plan only allows booking ${((r=c==null?void 0:c.advance_booking_days)==null?void 0:r.court)||10} days in advance. Please select a valid date.`;T({isOpen:!0,title:"Date Selection Error",message:u,type:"warning"}),E("main");return}const t=(d=m==null?void 0:m.sport_types)==null?void 0:d.some(u=>u.type&&u.type.trim()!==""),n=(a=m==null?void 0:m.sport_types)==null?void 0:a.find(u=>u.type===g),o=(y=n==null?void 0:n.subtype)==null?void 0:y.some(u=>u&&u.trim()!=="");if(!p||t&&!g||t&&o&&!f||!w||!h.length){T({isOpen:!0,title:"Incomplete Details",message:"Please complete all required Reservation detail",type:"warning"}),E("main");return}if((s==null?void 0:s.allow_user_court_selection)===1&&!v){T({isOpen:!0,title:"Court Selection Required",message:"Please select a court for your reservation",type:"warning"});return}if(h.length>0){const{duration:u}=Y(h),N=u*60;if(N<P){T({isOpen:!0,title:"Minimum Booking Time Not Met",message:`The minimum booking time requirement is ${P} minutes. Your current selection is ${N} minutes. Please select a longer time slot.`,type:"warning"}),E("main");return}}if(!x.length){T({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}try{be(!0);const u=await Mt();if(!u)throw new Error("Failed to create reservation");ft(u),E("payment")}catch(u){console.error("Reservation error:",u),T({isOpen:!0,title:"Reservation Error",message:u.message||"Error creating reservation",type:"error"})}finally{be(!1)}},It=t=>{const n=V(t.from,"h:mm A"),l=V(t.until,"h:mm A").diff(n,"minutes");if(v&&$.length>0){const r=$.find(a=>a.id===v),d=(r==null?void 0:r.min_booking_time)||P;if(l<d){S(j,`Minimum booking time for this court is ${d} minutes. Please select a longer time slot.`,4e3,"warning");return}}ie(null),ve([{from:t.from,until:t.until}])},O=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return C&&!C.allowCourt?e.jsx(Vt,{message:`Your current plan (${C==null?void 0:C.planName}) does not include court reservations. Please upgrade your plan to access this feature.`}):e.jsxs("div",{className:"",children:[e.jsx(Ut,{isOpen:A.isOpen,onClose:()=>T({...A,isOpen:!1}),title:A.title,message:A.message,actionButtonText:A.actionButtonText,actionButtonLink:A.actionButtonLink,onAction:A.onAction,type:A.type}),rt&&e.jsx(He,{}),e.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[R==="main"&&e.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),R==="players"&&e.jsx("div",{className:" ",children:"Step 2 • Reservation detail"}),R==="payment"&&e.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),e.jsxs("div",{className:"p-4",children:[e.jsx(Kt,{onBack:()=>{R==="main"?vt(-1):E(R==="payment"?"players":"main")}}),R==="main"?e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ts,{sports:L,userPermissions:C,initialSport:p,initialType:g,initialSubType:f,onSelectionChange:Tt,courts:D,filterMode:"reservation"},`${p}-${g}-${f}`),p&&(!(($e=m==null?void 0:m.sport_types)!=null&&$e.length)||g!==null&&(f!==null||!((Ie=(Be=(Pe=m==null?void 0:m.sport_types)==null?void 0:Pe.find(t=>t.type===g))==null?void 0:Be.subtype)!=null&&Ie.length)))?e.jsx(e.Fragment,{children:Se?e.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:e.jsxs("div",{className:"text-center",children:[e.jsx(He,{}),e.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Loading availability data..."})]})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[(Re=c==null?void 0:c.advance_booking_enabled)!=null&&Re.court?e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a court up to"," ",(Ae=c==null?void 0:c.advance_booking_days)==null?void 0:Ae.court," ",((Ee=c==null?void 0:c.advance_booking_days)==null?void 0:Ee.court)===1?"day":"days"," ","in advance."]}):e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court for any future date."}),e.jsx(es,{currentMonth:J,selectedDate:w,onDateSelect:t=>{var n,o;if(t>z){const l=(n=c==null?void 0:c.advance_booking_enabled)!=null&&n.court?`Your membership plan only allows booking ${((o=c==null?void 0:c.advance_booking_days)==null?void 0:o.court)||10} days in advance`:"";if(l){S(j,l,3e3,"warning");return}}Ge(t)},onPreviousMonth:bt,onNextMonth:Nt,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:z,disabledDateMessage:(Le=c==null?void 0:c.advance_booking_enabled)!=null&&Le.court?`Your membership plan only allows booking ${((qe=c==null?void 0:c.advance_booking_days)==null?void 0:qe.court)||10} days in advance`:"You can book for any future date"})]})}),w&&e.jsxs("div",{children:[p&&ne.length>0&&e.jsxs("div",{className:"mb-4 rounded-lg bg-blue-50 p-3",children:[e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("span",{className:"font-medium",children:"Minimum booking time:"})," ",e.jsxs("span",{className:"font-semibold text-blue-900",children:[Math.floor(P/60)," hour",Math.floor(P/60)!==1?"s":"",P%60!==0&&` ${P%60} minutes`]})]}),e.jsx("p",{className:"mt-1 text-xs text-blue-600",children:"Based on available courts for your selected sport, type, and subtype"})]}),e.jsx(zt,{selectedDate:w,timeRange:h,onTimeClick:It,isLoading:yt,onNext:()=>{var t,n;if(!h.length){S(j,"Please select a time slot",3e3,"error");return}if(w>z){const o=(t=c==null?void 0:c.advance_booking_enabled)!=null&&t.court?`Your membership plan only allows booking ${((n=c==null?void 0:c.advance_booking_days)==null?void 0:n.court)||10} days in advance`:"";if(o){S(j,o,3e3,"warning");return}}Pt()},nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",isTimeSlotAvailable:kt,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],minBookingTime:v&&$.length>0&&((Fe=$.find(t=>t.id===v))==null?void 0:Fe.min_booking_time)||P,enforceMinBookingTime:!!v,availabilityData:xt,loadingAvailability:Se})]})]})}):e.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}):R==="payment"?e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(Ve,{selectedSport:p,sports:L,selectedType:g,selectedSubType:f,selectedDate:w,selectedTimes:h,selectedCourt:v?ne.find(t=>t.id===v):null})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:H(G)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:H(ee(s==null?void 0:s.fee_settings,G))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:H(Z)})]}),e.jsxs("div",{children:[e.jsx(Jt,{user:b,bookingId:pt,reservationId:Ne,clientSecret:ct,paymentIntent:mt,navigateRoute:`/user/payment-success/${Ne}?type=court`}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:O==null?void 0:O.payment_description})})]}),e.jsx("div",{className:"space-y-4 text-sm text-gray-500",children:e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Ye,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Ye,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]})]})})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[" ",e.jsx(Ve,{selectedSport:p,sports:L,selectedType:g,selectedSubType:f,selectedDate:w,selectedTimes:h,selectedCourt:v?$.find(t=>t.id===v):null})]}),e.jsxs("div",{className:"space-y-4",children:[(s==null?void 0:s.allow_user_court_selection)===1&&e.jsxs("div",{className:"mb-2 h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Select Court"}),v&&$.length>0&&e.jsxs("div",{className:"mb-3 rounded-lg bg-green-50 p-2 text-xs text-green-700",children:[e.jsx("span",{className:"font-medium",children:(Oe=$.find(t=>t.id===v))==null?void 0:Oe.name})," ","has been automatically selected as the best match for your time slot. If another court that matches your requirements are available, you can select them from the dropdown. Courts with higher minimum booking time requirements are shown but disabled if your current time selection is too short."]}),e.jsx(ss,{className:"w-full text-sm",options:$.map(t=>{const n=t.min_booking_time||30,{duration:o}=h.length>0?Y(h):{duration:0},l=o*60,r=l>0&&n>l;let d=`${t.name} (Min: ${n}min)`;if(r){const a=Math.floor(n/60),y=n%60,u=a>0?`${a}h${y>0?` ${y}m`:""}`:`${y}m`;d+=` - Requires ${u} minimum`}return{value:t.id,label:d,isDisabled:r}}),onChange:t=>{if(ie(t?t.value:null),t&&h.length>0){const n=$.find(d=>d.id===t.value),o=(n==null?void 0:n.min_booking_time)||P,{duration:l}=Y(h);l*60<o&&(ve([]),S(j,`Selected court requires minimum ${o} minutes. Please select a new time slot.`,4e3,"info"))}},value:v?(()=>{const t=$.find(n=>n.id===v);if(t){const n=t.min_booking_time||30;return{value:v,label:`${t.name} (Min: ${n}min)`}}return null})():null,isClearable:!0,placeholder:"Select a court (available courts will appear after time selection)",noOptionsMessage:()=>{var n,o,l;if(!p)return"Please select a sport first";if((n=m==null?void 0:m.sport_types)!=null&&n.some(r=>r.type&&r.type.trim()!=="")&&!g)return"Please select a type";const t=(o=m==null?void 0:m.sport_types)==null?void 0:o.find(r=>r.type===g);return(l=t==null?void 0:t.subtype)!=null&&l.some(r=>r&&r.trim()!=="")&&!f?"Please select a sub-type":!w||!h.length?"Please select date and time first to see available courts":"No courts available for the selected time slot"}})]}),e.jsx(Xt,{players:De,groups:Qe,selectedPlayers:x,familyMembers:it,currentUser:_,onCurrentUserChange:$t,onPlayerToggle:t=>{if(x.some(o=>o.id===t.id)){if(t.id===(_==null?void 0:_.id)){S(j,"You cannot remove the primary player from the reservation",3e3,"warning");return}Me(t);return}if(x.length>=k){const o=(m==null?void 0:m.name)||"this sport";S(j,`Maximum ${k} players allowed for ${o} (including yourself)`,3e3,"warning");return}Me(t)},isFindBuddyEnabled:ue,setSelectedPlayers:Q,onFindBuddyToggle:()=>{Xe(!ue),et(!ge)},playersNeeded:te,onPlayersNeededChange:se,maximumPlayers:k,userProfile:b,showPlayersNeeded:ge,onNtrpMinChange:tt,onNtrpMaxChange:st,onShortBioChange:nt,initialNtrpMin:pe,initialNtrpMax:fe,initialShortBio:he})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation detail"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",x==null?void 0:x.length,")"]}),e.jsx("div",{className:"mt-1",children:x.length>0&&x.map(t=>e.jsxs("div",{className:"text-sm",children:[t.first_name," ",t.last_name]},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Club Fee"}),e.jsx("span",{children:H(G)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:H(ee(s==null?void 0:s.fee_settings,G))})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:H(Z)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Gt,{loading:gt,onClick:Bt,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("div",{className:"text-center text-sm text-gray-500",children:O==null?void 0:O.reservation_description}),e.jsx("div",{className:"space-y-2 text-center text-sm text-gray-500",children:e.jsx("p",{children:"(You will not be charged yet)"})})]})})]})]})]})]})};export{Xs as default};
